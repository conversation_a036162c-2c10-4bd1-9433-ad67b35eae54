/* 移动端地图页面专用样式 */

.map-controls {
    background-color: white;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    position: relative;
    z-index: 100;
}

.search-mini {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-mini input {
    flex: 1;
    margin: 0;
}

.map-filters {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.map-filters select {
    flex: 1;
    margin: 0;
}

.map-info {
    color: #666;
    font-size: 0.9rem;
    text-align: center;
}

/* 公司列表切换按钮 */
.company-list-toggle {
    background-color: white;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #eee;
    text-align: center;
}

.company-list-toggle .btn {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 移动端公司列表面板 */
.mobile-company-list {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid #ddd;
    border-radius: 12px 12px 0 0;
    max-height: 70vh;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
    box-shadow: 0 -4px 12px rgba(0,0,0,0.15);
}

.mobile-company-list.show {
    transform: translateY(0);
}

.mobile-list-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 11;
}

.mobile-list-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.mobile-list-content {
    padding: 0;
    overflow-y: auto;
    max-height: calc(70vh - 80px);
}

.mobile-company-item {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-company-item:hover,
.mobile-company-item.active {
    background-color: #f8f9fa;
}

.mobile-company-item.active {
    border-left: 4px solid #3498db;
    background-color: #e3f2fd;
}

.mobile-item-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.mobile-item-name-jp {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.mobile-item-industry {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
}

.mobile-item-address {
    font-size: 0.85rem;
    color: #666;
    line-height: 1.3;
}

/* 移动端拜访状态样式 */
.mobile-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.visit-status {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-weight: 500;
}

.visit-status:not(.unvisited) {
    background-color: #d4edda;
    color: #155724;
}

.visit-status.unvisited {
    background-color: #f8d7da;
    color: #721c24;
}

/* 移动端访问状态选择框样式 */
.visit-status-select.mobile {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    background-color: white;
    color: #333;
    font-weight: 500;
    cursor: pointer;
    min-width: 70px;
    transition: all 0.2s ease;
}

.visit-status-select.mobile:hover {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.visit-status-select.mobile:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.visit-status-select.mobile option[value="visited"] {
    background-color: #d4edda;
    color: #155724;
}

.visit-status-select.mobile option[value="unvisited"] {
    background-color: #f8d7da;
    color: #721c24;
}

.mobile-company-item.visited {
    border-left: 4px solid #27ae60;
    background-color: #f8fff9;
}

.mobile-company-item.visited:hover {
    background-color: #e8f5e8;
}

.mobile-item-notes {
    font-size: 0.75rem;
    color: #666;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 0.5rem;
    border-left: 3px solid #27ae60;
}

/* 移动端备注摘要样式 */
.memo-count {
    font-size: 0.7rem;
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.25rem;
}

.memo-latest {
    font-size: 0.7rem;
    color: #666;
    line-height: 1.3;
    max-height: 2.6em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 地图容器 */
.map-container {
    height: calc(100vh - 200px);
    min-height: 400px;
    position: relative;
    background-color: #f0f0f0;
}

/* 自定义地图控件 */
.amap-logo,
.amap-copyright {
    display: none !important;
}

/* 地图标记样式 */
.custom-marker {
    background-color: #e74c3c;
    border: 3px solid white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.custom-marker:hover {
    transform: scale(1.1);
    background-color: #c0392b;
}

.custom-marker.active {
    background-color: #f39c12;
    border-color: #fff;
    transform: scale(1.2);
}

/* 移动端拜访状态标记样式 */
.custom-marker.visited {
    background-color: #27ae60; /* 绿色表示已拜访 */
}

.custom-marker.unvisited {
    background-color: #e74c3c; /* 红色表示未拜访 */
}

.custom-marker.visited.active {
    background-color: #2ecc71; /* 选中的已拜访公司 */
}

.custom-marker.unvisited.active {
    background-color: #f39c12; /* 选中的未拜访公司 */
}

/* 标记点上方的信息标签 */
.marker-label {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 11px;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    max-width: 200px;
    text-align: left;
    position: relative;
    margin-bottom: 5px;
}

.marker-label::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: white;
}

.marker-label-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
    font-size: 12px;
    line-height: 1.2;
}

.marker-label-industry {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 1px 5px;
    border-radius: 8px;
    font-size: 9px;
    margin-bottom: 2px;
}

.marker-label-address {
    color: #666;
    font-size: 10px;
    line-height: 1.2;
}

/* 聚合标记样式 */
.cluster-marker {
    background-color: #3498db;
    border: 3px solid white;
    border-radius: 50%;
    color: white;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.cluster-marker:hover {
    transform: scale(1.1);
    background-color: #2980b9;
}

.cluster-marker.small {
    width: 35px;
    height: 35px;
    font-size: 12px;
}

.cluster-marker.medium {
    width: 45px;
    height: 45px;
    font-size: 14px;
}

.cluster-marker.large {
    width: 55px;
    height: 55px;
    font-size: 16px;
}

/* 详情面板 */
.company-panel,
.cluster-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.panel-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background-color: #e9ecef;
    color: #333;
}

.panel-content {
    padding: 1rem;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

/* 地图覆盖层 */
.map-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
    display: none;
}

.map-overlay.show {
    display: block;
}

.company-panel.show,
.cluster-panel.show {
    display: block;
}

/* 详情面板内容样式 */
.company-detail {
    margin-bottom: 1.5rem;
}

.company-detail h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.company-detail-jp {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.company-industry-tag {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.detail-label {
    font-weight: 500;
    color: #555;
    min-width: 80px;
    margin-right: 1rem;
    margin-bottom: 0.25rem;
}

.detail-value {
    color: #333;
    flex: 1;
    word-break: break-all;
}

.detail-value a {
    color: #3498db;
    text-decoration: none;
}

.detail-value a:hover {
    text-decoration: underline;
}

/* 聚合面板样式 */
.cluster-summary {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    text-align: center;
}

.cluster-count {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.cluster-area {
    color: #666;
    font-size: 1rem;
}

.cluster-companies {
    max-height: 300px;
    overflow-y: auto;
}

.cluster-company-item {
    padding: 0.75rem;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cluster-company-item:hover {
    background-color: #f8f9fa;
    border-color: #3498db;
}

.cluster-company-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.cluster-company-address {
    font-size: 0.8rem;
    color: #666;
}

/* 移动端备注编辑样式 */
.notes-textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
}

.notes-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}
