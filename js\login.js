// 登录页面功能
class LoginManager {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.checkAutoLogin();
    }
    
    initializeElements() {
        this.loginForm = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.loginButton = document.getElementById('loginButton');
        this.buttonText = this.loginButton.querySelector('.button-text');
        this.loadingSpinner = this.loginButton.querySelector('.loading-spinner');
        this.errorMessage = document.getElementById('errorMessage');
        this.successMessage = document.getElementById('successMessage');
    }
    
    bindEvents() {
        // 表单提交事件
        this.loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
        
        // 输入框回车事件
        this.usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.passwordInput.focus();
            }
        });
        
        this.passwordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin();
            }
        });
        
        // 清除错误消息
        [this.usernameInput, this.passwordInput].forEach(input => {
            input.addEventListener('input', () => {
                this.hideMessages();
            });
        });
    }
    
    /**
     * 处理登录逻辑
     */
    async handleLogin() {
        // 验证输入
        if (!this.validateInputs()) {
            return;
        }
        
        // 显示加载状态
        this.setLoadingState(true);
        this.hideMessages();
        
        try {
            // 获取登录数据
            const loginData = {
                username: this.usernameInput.value.trim(),
                password: this.passwordInput.value
            };
            
            // 加密登录数据
            console.log('开始加密登录数据...');
            const encryptedPayload = window.EncryptionUtils.encryptLoginData(loginData);
            
            // 验证加密数据
            if (!window.EncryptionUtils.validatePayload(encryptedPayload)) {
                throw new Error('加密数据验证失败');
            }
            
            // 发送登录请求
            const response = await this.sendLoginRequest(encryptedPayload);
            
            console.log('API请求成功:', response);
            
            // 处理登录结果
            await this.handleLoginResponse(response, loginData.username);
            
        } catch (error) {
            console.error('登录失败:', error);
            this.showError(error.message || '登录过程中发生错误，请重试');
        } finally {
            this.setLoadingState(false);
        }
    }
    
    /**
     * 验证输入数据
     */
    validateInputs() {
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value;
        
        if (!username) {
            this.showError('アカウント名を入力してください');
            this.usernameInput.focus();
            return false;
        }
        
        if (username.length < 3) {
            this.showError('アカウント名は3文字以上で入力してください');
            this.usernameInput.focus();
            return false;
        }
        
        if (!password) {
            this.showError('パスワードを入力してください');
            this.passwordInput.focus();
            return false;
        }
        
        if (password.length < 6) {
            this.showError('パスワードは6文字以上で入力してください');
            this.passwordInput.focus();
            return false;
        }
        
        return true;
    }
    
    /**
     * 发送登录请求
     */
    async sendLoginRequest(encryptedPayload) {
        const loginUrl = 'https://metaverse.fujisoft-china.com:7796/companymap-api/auth/signin'; // 实际的登录API端点
        
        // 模拟API请求（实际项目中替换为真实API）
        // return new Promise((resolve, reject) => {
        //     setTimeout(() => {
        //         // 模拟服务器响应
        //         const mockResponse = this.getMockLoginResponse(encryptedPayload);
                
        //         if (mockResponse.success) {
        //             resolve(mockResponse);
        //         } else {
        //             reject(new Error(mockResponse.message));
        //         }
        //     }, 1500); // 模拟网络延迟
        // });
        
        //实际API请求代码：
        try {
            const response = await fetch(loginUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(encryptedPayload)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw new Error('ネットワークエラーが発生しました');
        }
    }
    
    /**
     * 模拟服务器响应（仅用于演示）
     */
    getMockLoginResponse(encryptedPayload) {
        console.log('模拟服务器接收到加密数据:', encryptedPayload);
        
        // 模拟不同的登录结果
        const mockUsers = {
            'admin': { password: 'admin123', role: 'admin' },
            'user': { password: 'user123', role: 'user' },
            'test': { password: 'test123', role: 'user' }
        };
        
        // 在实际项目中，服务器会解密数据进行验证
        // 这里仅为演示目的，直接返回成功
        return {
            success: true,
            message: 'ログイン成功',
            data: {
                token: 'mock_jwt_token_' + Date.now(),
                user: {
                    username: 'demo_user',
                    role: 'user',
                    loginTime: new Date().toISOString()
                }
            }
        };
    }
    
    /**
     * 处理登录响应
     */
    async handleLoginResponse(response, username) {
        if (response.code== 200) {
            // 保存登录状态
            this.saveLoginState(response.data, username);
            
            // 显示成功消息
            this.showSuccess('ログインに成功しました！リダイレクト中...');
            
            // 延迟跳转到主页
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
            
        } else {
            throw new Error(response.msg || 'ログインに失敗しました');
        }
    }
    
    /**
     * 保存登录状态
     */
    saveLoginState(data, username) {
        try {
            // 保存到localStorage
            localStorage.setItem('authToken', data.token);
            localStorage.setItem('userInfo', username);
            localStorage.setItem('userId', data.userId);
            localStorage.setItem('realName', data.realName);
            localStorage.setItem('mail', data.mail);
            localStorage.setItem('loginTime', Date.now().toString());
            
            // 保存到sessionStorage（会话级别）
            sessionStorage.setItem('isLoggedIn', 'true');
            sessionStorage.setItem('currentUser', username);
            
            console.log('登录状态已保存');
        } catch (error) {
            console.error('保存登录状态失败:', error);
        }
    }
    
    /**
     * 检查自动登录
     */
    checkAutoLogin() {
        const token = localStorage.getItem('authToken');
        const loginTime = localStorage.getItem('loginTime');
        
        if (token && loginTime) {
            const elapsed = Date.now() - parseInt(loginTime);
            const maxAge = 24 * 60 * 60 * 1000; // 24小时
            
            if (elapsed < maxAge) {
                // 自动跳转到主页
                console.log('检测到有效登录状态，自动跳转...');
                window.location.href = 'index.html';
                return;
            } else {
                // 清除过期的登录状态
                this.clearLoginState();
            }
        }
    }
    
    /**
     * 清除登录状态
     */
    clearLoginState() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('loginTime');
        sessionStorage.removeItem('isLoggedIn');
        sessionStorage.removeItem('currentUser');
    }
    
    /**
     * 设置加载状态
     */
    setLoadingState(loading) {
        this.loginButton.disabled = loading;
        
        if (loading) {
            this.buttonText.style.opacity = '0';
            this.loadingSpinner.style.display = 'block';
        } else {
            this.buttonText.style.opacity = '1';
            this.loadingSpinner.style.display = 'none';
        }
    }
    
    /**
     * 显示错误消息
     */
    showError(message) {
        this.hideMessages();
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        
        // 自动隐藏错误消息
        setTimeout(() => {
            this.hideMessages();
        }, 5000);
    }
    
    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.hideMessages();
        this.successMessage.textContent = message;
        this.successMessage.style.display = 'block';
    }
    
    /**
     * 隐藏所有消息
     */
    hideMessages() {
        this.errorMessage.style.display = 'none';
        this.successMessage.style.display = 'none';
    }
}

// 页面加载完成后初始化登录管理器
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});
