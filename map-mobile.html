<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地図検索（モバイル版） - Company Search System</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/map-mobile.css">
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=b9c03cb6a5d05c83348d2c9c5636a3c9"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1 class="logo">会社検索システム</h1>
            <nav class="nav">
                <a href="index.html" class="nav-link">検索</a>
                <a href="map.html" class="nav-link">PC版地図</a>
                <a href="map-mobile.html" class="nav-link active">モバイル版地図</a>
                <div class="nav-user" id="navUser" style="display: none;">
                    <span class="user-name" id="userName"></span>
                    <button class="logout-btn" id="logoutBtn">ログアウト</button>
                </div>
                <a href="login.html" class="nav-link" id="loginLink">ログイン</a>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container-fluid">
            <!-- Map Controls -->
            <div class="map-controls">
                <div class="search-mini">
                    <input type="text" id="map-search" placeholder="会社名や地域で検索">
                    <button id="map-search-btn" class="btn btn-primary">検索</button>
                </div>
                <div class="map-filters">
                    <select id="map-industry">
                        <option value="">業界で絞り込み</option>
                        <option value="manufacturing">製造業</option>
                        <option value="finance">金融業</option>
                        <option value="technology">IT・技術</option>
                        <option value="retail">小売業</option>
                        <option value="healthcare">医療・健康</option>
                        <option value="education">教育</option>
                        <option value="construction">建設業</option>
                        <option value="logistics">物流</option>
                    </select>
                    <button id="clear-map-filters" class="btn btn-secondary">クリア</button>
                </div>
                <div class="map-info">
                    <span id="map-results-count">すべての会社を地図上に表示中</span>
                </div>
            </div>

            <!-- Company List Toggle Button -->
            <div class="company-list-toggle">
                <button id="toggle-company-list" class="btn btn-outline">
                    <span id="toggle-text">会社リストを表示</span>
                    <span id="company-count">(0社)</span>
                </button>
            </div>

            <!-- Mobile Company List Panel -->
            <div class="mobile-company-list" id="mobile-company-list">
                <div class="mobile-list-header">
                    <h3 class="mobile-list-title">表示中の会社</h3>
                    <button id="close-mobile-list" class="close-btn">&times;</button>
                </div>
                <div class="mobile-list-content" id="mobile-list-content">
                    <!-- 公司列表将在这里动态生成 -->
                </div>
            </div>

            <!-- Map Container -->
            <div id="map-container" class="map-container">
                <!-- Amap will be initialized here -->
            </div>

            <!-- Company Details Panel -->
            <div id="company-panel" class="company-panel">
                <div class="panel-header">
                    <h3 id="panel-title">会社詳細</h3>
                    <button id="close-panel" class="close-btn">&times;</button>
                </div>
                <div id="panel-content" class="panel-content">
                    <!-- Company details will be populated here -->
                </div>
            </div>

            <!-- Cluster Details Panel -->
            <div id="cluster-panel" class="cluster-panel">
                <div class="panel-header">
                    <h3 id="cluster-title">エリア詳細</h3>
                    <button id="close-cluster-panel" class="close-btn">&times;</button>
                </div>
                <div id="cluster-content" class="panel-content">
                    <!-- Cluster details will be populated here -->
                </div>
            </div>

            <!-- Map Overlay -->
            <div class="map-overlay"></div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Company Search System. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/map-mobile.js"></script>
</body>
</html>
