<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问状态测试页面</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/map.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        
        .test-title {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .company-list-item {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .company-list-item.visited {
            border-left: 4px solid #28a745;
            background-color: #f8fff9;
        }
        
        .company-list-item.unvisited {
            border-left: 4px solid #dc3545;
            background-color: #fff5f5;
        }
        
        .status-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <h1 class="logo">访问状态功能测试</h1>
        </div>
    </header>

    <main class="main">
        <div class="test-container">
            <h2>访问状态选择框功能测试</h2>
            <p>这个页面用于测试新的访问状态选择框功能。选择框允许用户手动设置公司的访问状态。</p>
            
            <div class="test-section">
                <div class="test-title">测试公司列表</div>
                <div id="test-company-list">
                    <!-- 公司列表将在这里动态生成 -->
                </div>
                <div class="status-info">
                    <strong>功能说明：</strong>
                    <ul>
                        <li>选择"訪問済み"会自动添加默认备注到服务器</li>
                        <li>选择"未訪問"会删除当前用户的备注</li>
                        <li>状态变化会实时同步到所有页面</li>
                        <li>选择框样式会根据状态自动调整</li>
                    </ul>
                </div>
            </div>
            
            <div class="test-section">
                <div class="test-title">测试步骤</div>
                <ol>
                    <li>观察初始状态：有备注的公司显示为"訪問済み"，无备注显示为"未訪問"</li>
                    <li>点击选择框，切换访问状态</li>
                    <li>观察页面实时更新：边框颜色、背景色等视觉效果</li>
                    <li>打开浏览器开发者工具查看网络请求</li>
                    <li>刷新页面验证状态是否持久化</li>
                </ol>
            </div>
        </div>
    </main>

    <script src="js/auth.js"></script>
    <script src="js/data.js"></script>
    <script>
        class VisitStatusTest {
            constructor() {
                this.companies = [];
                this.init();
            }
            
            async init() {
                try {
                    this.companies = await DataUtils.getAllCompanies();
                    this.renderCompanyList();
                } catch (error) {
                    console.error('Failed to load companies:', error);
                    document.getElementById('test-company-list').innerHTML = 
                        '<p style="color: red;">数据加载失败，请检查网络连接和认证状态</p>';
                }
            }
            
            renderCompanyList() {
                const container = document.getElementById('test-company-list');
                
                // 只显示前5个公司用于测试
                const testCompanies = this.companies.slice(0, 5);
                
                const html = testCompanies.map(company => {
                    const isVisited = this.isCompanyVisited(company);
                    return `
                        <div class="company-list-item ${isVisited ? 'visited' : 'unvisited'}" data-company-id="${company.id}">
                            <div class="company-item-header">
                                <div class="company-item-name">${company.name}</div>
                                <select class="visit-status-select" data-company-id="${company.id}">
                                    <option value="unvisited" ${!isVisited ? 'selected' : ''}>未訪問</option>
                                    <option value="visited" ${isVisited ? 'selected' : ''}>訪問済み</option>
                                </select>
                            </div>
                            <div class="company-item-name-jp">${company.nameJp}</div>
                            <div class="company-item-address">${company.district} - ${company.address}</div>
                            <div style="margin-top: 0.5rem;">
                                <strong>备注数量:</strong> ${company.companyMemoList ? company.companyMemoList.length : 0}
                            </div>
                        </div>
                    `;
                }).join('');
                
                container.innerHTML = html;
                this.bindEvents();
            }
            
            bindEvents() {
                document.querySelectorAll('.visit-status-select').forEach(select => {
                    select.addEventListener('change', (e) => {
                        const companyId = parseInt(select.dataset.companyId);
                        const newStatus = select.value;
                        this.updateVisitStatus(companyId, newStatus);
                    });
                });
            }
            
            isCompanyVisited(company) {
                return company.companyMemoList && company.companyMemoList.length > 0;
            }
            
            updateVisitStatus(companyId, newStatus) {
                const company = this.companies.find(c => c.id === companyId);
                if (!company) return;

                console.log(`更新公司 ${company.name} 的访问状态为: ${newStatus}`);

                if (newStatus === 'visited') {
                    if (!company.companyMemoList || company.companyMemoList.length === 0) {
                        this.saveVisitStatusToServer({
                            id: '',
                            memo: '訪問済み',
                            userId: localStorage.getItem('userId'),
                            realName: localStorage.getItem('realName'),
                            companyId: companyId
                        });
                    }
                } else if (newStatus === 'unvisited') {
                    if (company.companyMemoList && company.companyMemoList.length > 0) {
                        const userMemo = company.companyMemoList.find(memo => 
                            memo.userId == localStorage.getItem('userId')
                        );
                        if (userMemo) {
                            this.deleteVisitStatusFromServer(userMemo.id, companyId);
                        }
                    }
                }
            }
            
            saveVisitStatusToServer(memoData) {
                const requestData = {
                    id: memoData.id || '',
                    memo: memoData.memo,
                    companyId: memoData.companyId
                };

                fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/companyMemo/insertOrUpdate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Authorization': 'Bearer ' + localStorage.getItem('authToken')
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(res => {
                    console.log('访问状态保存成功:', res);
                    if (res.code == 200 && res.data) {
                        const company = this.companies.find(c => c.id === memoData.companyId);
                        if (company) {
                            if (!company.companyMemoList) {
                                company.companyMemoList = [];
                            }
                            
                            const newMemo = {
                                id: res.data.id,
                                memo: memoData.memo,
                                userId: localStorage.getItem('userId'),
                                realName: localStorage.getItem('realName')
                            };
                            
                            company.companyMemoList.push(newMemo);
                            this.renderCompanyList();
                        }
                    }
                })
                .catch(error => {
                    console.error('访问状态保存失败:', error);
                    alert('访问状态保存失败，请重试');
                });
            }
            
            deleteVisitStatusFromServer(memoId, companyId) {
                const requestData = {
                    id: memoId,
                    memo: '',
                    companyId: companyId
                };

                fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/companyMemo/insertOrUpdate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Authorization': 'Bearer ' + localStorage.getItem('authToken')
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => response.json())
                .then(res => {
                    console.log('访问状态删除成功:', res);
                    if (res.code == 200) {
                        const company = this.companies.find(c => c.id === companyId);
                        if (company && company.companyMemoList) {
                            company.companyMemoList = company.companyMemoList.filter(memo =>
                                memo.userId != localStorage.getItem('userId')
                            );
                            this.renderCompanyList();
                        }
                    }
                })
                .catch(error => {
                    console.error('访问状态删除失败:', error);
                    alert('访问状态删除失败，请重试');
                });
            }
        }

        // 页面加载完成后初始化测试
        document.addEventListener('DOMContentLoaded', () => {
            new VisitStatusTest();
        });
    </script>
</body>
</html>
