# Company Search System (会社検索システム)

A web application for searching and displaying company information with both traditional search interface and interactive map view using Amap (高德地图).

## Features

### Page 1: Traditional Search Interface
- Search/filter interface with multiple criteria
- Input fields for company name, industry, location
- Dropdown/select elements for filtering options
- Search results displayed in list format with pagination
- Responsive design for desktop and mobile devices
- Professional UI styling with CSS

### Page 2: Interactive Map View
- Amap (高德地图) JavaScript SDK integration
- Companies displayed as markers on the map
- Clustering functionality:
  - Groups companies by district/county when zoomed out
  - Shows aggregated count for each cluster
  - Allows zoom in to see individual company details
  - Adjusts clustering based on map zoom level
- Map controls for navigation and zoom
- Company information popups when clicking markers

## Technical Stack

- **Frontend**: Vanilla HTML, CSS, and JavaScript (no frameworks)
- **Map Service**: Amap (高德地图) JavaScript SDK
- **Styling**: Custom CSS with responsive design
- **Data**: JSON-based sample company data

## Setup Instructions

### 1. Get Amap API Key

1. Visit [Amap Developer Console](https://console.amap.com/)
2. Register for a developer account
3. Create a new application
4. Get your JavaScript API key

### 2. Configure API Key

Edit `map.html` and replace `YOUR_AMAP_API_KEY` with your actual API key:

```html
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=YOUR_ACTUAL_API_KEY"></script>
```

### 3. Run the Application

Since this is a static web application, you can run it in several ways:

#### Option 1: Local Web Server (Recommended)
```bash
# Using Python 3
python -m http.server 8000

# Using Node.js (if you have http-server installed)
npx http-server

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

#### Option 2: Direct File Access
You can open `index.html` directly in your browser, but some features may not work due to CORS restrictions.

## Project Structure

```
mapForHtml/
├── index.html          # Search interface page
├── map.html           # Interactive map page
├── css/
│   ├── styles.css     # Base styles and common components
│   ├── search.css     # Search page specific styles
│   └── map.css        # Map page specific styles
├── js/
│   ├── data.js        # Sample company data and utility functions
│   ├── search.js      # Search page functionality
│   └── map.js         # Map page functionality
└── README.md          # This file
```

## Usage

### Search Page (index.html)
1. Enter search criteria in the form fields
2. Click "検索" (Search) to find companies
3. Use the sort dropdown to change result ordering
4. Navigate through results using pagination
5. Click "地図で確認する" (View on Map) to see a company on the map
6. Click on company items to view detailed information

### Map Page (map.html)
1. Use the search bar to filter companies on the map
2. Use the industry dropdown to filter by business type
3. Zoom in/out to see individual markers or clusters
4. Click on clusters to see companies in that area
5. Click on individual markers to see company details
6. Use map controls for navigation

## Data Structure

Each company in the dataset includes:
- `id`: Unique identifier
- `name`: Company name (Chinese)
- `nameJp`: Japanese company name
- `industry`: Industry category
- `prefecture`: Prefecture/Province
- `city`: City
- `address`: Full address
- `coordinates`: [longitude, latitude] for map display
- `district`: District/County for clustering
- `phone`: Contact phone number
- `website`: Company website
- `employees`: Employee count range
- `established`: Year established

## Customization

### Adding More Companies
Edit `js/data.js` and add new company objects to the `companiesData` array.

### Modifying Industries
Update the industry options in both HTML files and the `industryMapping` object in `data.js`.

### Styling Changes
Modify the CSS files in the `css/` directory to customize the appearance.

### Map Configuration
Adjust map settings in `js/map.js`:
- Initial zoom level
- Center coordinates
- Map style
- Clustering thresholds

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Known Limitations

1. **API Key Required**: The map functionality requires a valid Amap API key
2. **CORS Restrictions**: Some features may not work when opening files directly in browser
3. **Sample Data**: Currently uses static sample data; in production, this would connect to a real database
4. **Language**: Interface is primarily in Japanese/Chinese

## Development Notes

- The application uses modern JavaScript features (ES6+)
- CSS Grid and Flexbox are used for responsive layouts
- No external dependencies except for the Amap SDK
- Code is organized with separation of concerns
- Error handling is implemented for map loading failures

## License

This project is for demonstration purposes. Please ensure you comply with Amap's terms of service when using their API.
