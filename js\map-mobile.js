// 移动端地图页面功能，集成高德地图
class MobileCompanyMap {
    constructor() {
        this.map = null;
        this.markers = [];
        this.clusters = [];
        this.currentZoom = 10; // 降低初始缩放级别以显示聚合
        this.filteredCompanies = DataUtils.getAllCompanies();
        this.selectedCompanyId = null;
        this.isListVisible = false;
        
        this.initializeElements();
        this.bindEvents();
        this.initializeMap();
    }
    
    initializeElements() {
        // 地图控制元素
        this.mapSearchInput = document.getElementById('map-search');
        this.mapSearchBtn = document.getElementById('map-search-btn');
        this.mapIndustrySelect = document.getElementById('map-industry');
        this.clearFiltersBtn = document.getElementById('clear-map-filters');
        this.mapResultsCount = document.getElementById('map-results-count');
        
        // 移动端列表元素
        this.toggleListBtn = document.getElementById('toggle-company-list');
        this.toggleText = document.getElementById('toggle-text');
        this.companyCount = document.getElementById('company-count');
        this.mobileCompanyList = document.getElementById('mobile-company-list');
        this.closeMobileListBtn = document.getElementById('close-mobile-list');
        this.mobileListContent = document.getElementById('mobile-list-content');
        
        // 面板元素
        this.companyPanel = document.getElementById('company-panel');
        this.clusterPanel = document.getElementById('cluster-panel');
        this.closePanelBtn = document.getElementById('close-panel');
        this.closeClusterPanelBtn = document.getElementById('close-cluster-panel');
        this.panelContent = document.getElementById('panel-content');
        this.clusterContent = document.getElementById('cluster-content');
    }
    
    bindEvents() {
        // 搜索功能
        this.mapSearchBtn.addEventListener('click', () => this.performMapSearch());
        this.mapSearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performMapSearch();
            }
        });
        
        // 筛选功能
        this.mapIndustrySelect.addEventListener('change', () => this.applyFilters());
        this.clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        
        // 移动端列表切换
        this.toggleListBtn.addEventListener('click', () => this.toggleCompanyList());
        this.closeMobileListBtn.addEventListener('click', () => this.hideCompanyList());
        
        // 面板关闭事件
        this.closePanelBtn.addEventListener('click', () => this.closeCompanyPanel());
        this.closeClusterPanelBtn.addEventListener('click', () => this.closeClusterPanel());
        
        // 点击外部关闭面板
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('map-overlay')) {
                this.closeCompanyPanel();
                this.closeClusterPanel();
            }
        });
    }
    
    initializeMap() {
        // 检查高德地图是否可用
        if (typeof AMap === 'undefined') {
            console.error('高德地图未加载。请检查您的API密钥和网络连接。');
            this.showMapError();
            return;
        }
        
        try {
            // 初始化地图，中心点设置为杭州
            this.map = new AMap.Map('map-container', {
                zoom: this.currentZoom,
                center: [120.2103, 30.2467], // 杭州坐标
                mapStyle: 'amap://styles/normal',
                features: ['bg', 'road', 'building'],
                viewMode: '2D'
            });
            
            // 添加地图控件
            AMap.plugin(['AMap.Scale', 'AMap.ToolBar'], () => {
                this.map.addControl(new AMap.Scale());
                this.map.addControl(new AMap.ToolBar());
            });
            
            // 绑定地图事件
            this.map.on('zoomend', () => {
                this.currentZoom = this.map.getZoom();
                this.updateMarkersAndClusters();
            });
            
            this.map.on('moveend', () => {
                this.updateMarkersAndClusters();
            });
            
            // 加载公司备注数据
            this.loadCompanyNotes();

            // 初始显示所有公司标记
            this.updateMarkersAndClusters();
            this.updateCompanyList();

            // 检查是否有从搜索页面选中的公司
            this.checkForSelectedCompany();

        } catch (error) {
            console.error('地图初始化错误:', error);
            this.showMapError();
        }
    }
    
    showMapError() {
        const mapContainer = document.getElementById('map-container');
        mapContainer.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background-color: #f5f5f5;">
                <div style="text-align: center; padding: 2rem;">
                    <h3 style="color: #666; margin-bottom: 1rem;">地図を読み込めませんでした</h3>
                    <p style="color: #999;">AMap APIキーを確認してください</p>
                    <p style="color: #999; font-size: 0.9rem;">デモ用のため、実際のAPIキーが必要です</p>
                </div>
            </div>
        `;
    }
    
    checkForSelectedCompany() {
        // 检查是否有从搜索页面选中的公司
        const selectedCompanyId = sessionStorage.getItem('selectedCompanyId');
        if (selectedCompanyId) {
            sessionStorage.removeItem('selectedCompanyId');

            const company = DataUtils.getCompanyById(parseInt(selectedCompanyId));
            if (company && this.map) {
                // 设置选中状态
                this.selectCompany(parseInt(selectedCompanyId));

                // 将地图中心设置为公司位置
                this.map.setCenter(company.coordinates);
                this.map.setZoom(15);

                // 更新标记状态
                this.updateMarkerStates();

                // 短暂延迟后显示公司详情
                setTimeout(() => {
                    this.showCompanyDetails(company);
                }, 1000);
            }
        }
    }
    
    performMapSearch() {
        const searchTerm = this.mapSearchInput.value.trim();
        if (!searchTerm) {
            // 如果没有搜索关键字，显示所有公司
            this.filteredCompanies = DataUtils.getAllCompanies();
            this.applyIndustryFilter();
        } else {
            // 根据搜索条件筛选公司
            const criteria = { name: searchTerm };
            this.filteredCompanies = DataUtils.searchCompanies(criteria);
            
            // 应用行业筛选
            this.applyIndustryFilter();
        }
        
        // 更新显示
        this.updateMarkersAndClusters();
        this.updateResultsCount();
        this.updateCompanyList();
    }
    
    applyFilters() {
        // 从所有公司开始
        this.filteredCompanies = DataUtils.getAllCompanies();
        
        // 如果存在搜索条件则应用搜索筛选
        const searchTerm = this.mapSearchInput.value.trim();
        if (searchTerm) {
            const criteria = { name: searchTerm };
            this.filteredCompanies = DataUtils.searchCompanies(criteria);
        }
        
        // 应用行业筛选
        this.applyIndustryFilter();
        
        // 更新显示
        this.updateMarkersAndClusters();
        this.updateResultsCount();
        this.updateCompanyList();
    }
    
    applyIndustryFilter() {
        const selectedIndustry = this.mapIndustrySelect.value;
        if (selectedIndustry) {
            this.filteredCompanies = this.filteredCompanies.filter(
                company => company.industry === selectedIndustry
            );
        }
    }
    
    clearFilters() {
        this.mapSearchInput.value = '';
        this.mapIndustrySelect.value = '';
        this.filteredCompanies = DataUtils.getAllCompanies();
        
        this.updateMarkersAndClusters();
        this.updateResultsCount();
        this.updateCompanyList();
    }
    
    updateResultsCount() {
        const count = this.filteredCompanies.length;
        const searchTerm = this.mapSearchInput.value.trim();
        const industryFilter = this.mapIndustrySelect.value;
        
        if (count === 0) {
            this.mapResultsCount.textContent = '条件に一致する会社が見つかりませんでした';
        } else if (!searchTerm && !industryFilter) {
            this.mapResultsCount.textContent = `すべての会社を地図上に表示中 (${count}社)`;
        } else {
            this.mapResultsCount.textContent = `${count}社を地図上に表示中`;
        }
    }
    
    toggleCompanyList() {
        if (this.isListVisible) {
            this.hideCompanyList();
        } else {
            this.showCompanyList();
        }
    }
    
    showCompanyList() {
        this.mobileCompanyList.classList.add('show');
        this.isListVisible = true;
        this.toggleText.textContent = '会社リストを非表示';
    }
    
    hideCompanyList() {
        this.mobileCompanyList.classList.remove('show');
        this.isListVisible = false;
        this.toggleText.textContent = '会社リストを表示';
    }
    
    updateCompanyList() {
        const count = this.filteredCompanies.length;
        this.companyCount.textContent = `(${count}社)`;
        
        if (count === 0) {
            this.mobileListContent.innerHTML = `
                <div style="padding: 2rem; text-align: center; color: #666;">
                    <p>表示する会社がありません</p>
                </div>
            `;
            return;
        }
        
        // 按区域排序公司
        const sortedCompanies = [...this.filteredCompanies].sort((a, b) => {
            if (a.district !== b.district) {
                return a.district.localeCompare(b.district);
            }
            return a.name.localeCompare(b.name);
        });
        
        const html = sortedCompanies.map(company => {
            const isVisited = this.isCompanyVisited(company);
            return `
                <div class="mobile-company-item ${isVisited ? 'visited' : ''}" data-company-id="${company.id}">
                    <div class="mobile-item-header">
                        <div class="mobile-item-name">${company.name}</div>
                        ${isVisited ? '<span class="visit-status">✓ 訪問済み</span>' : '<span class="visit-status unvisited">未訪問</span>'}
                    </div>
                    <div class="mobile-item-name-jp">${company.nameJp}</div>
                    <div class="mobile-item-industry">${DataUtils.getIndustryDisplayName(company.industry)}</div>
                    <div class="mobile-item-address">${company.district} - ${company.address}</div>
                    ${isVisited ? this.generateMemoSummary(company.companyMemoList) : ''}
                </div>
            `;
        }).join('');
        
        this.mobileListContent.innerHTML = html;
        
        // 绑定点击事件
        this.bindMobileListEvents();
    }

    // 更新公司列表显示（用于实时更新）
    updateCompanyListDisplay() {
        const count = this.filteredCompanies.length;
        this.companyCount.textContent = `${count}社`;

        if (count === 0) {
            this.mobileListContent.innerHTML = `
                <div style="padding: 2rem; text-align: center; color: #666;">
                    <p>表示する会社がありません</p>
                </div>
            `;
            return;
        }

        // 按区域排序公司
        const sortedCompanies = [...this.filteredCompanies].sort((a, b) => {
            if (a.district !== b.district) {
                return a.district.localeCompare(b.district);
            }
            return a.name.localeCompare(b.name);
        });

        const html = sortedCompanies.map(company => {
            const isVisited = this.isCompanyVisited(company);
            return `
                <div class="mobile-company-item ${isVisited ? 'visited' : ''}" data-company-id="${company.id}">
                    <div class="mobile-item-header">
                        <div class="mobile-item-name">${company.name}</div>
                        ${isVisited ? '<span class="visit-status">✓ 訪問済み</span>' : '<span class="visit-status unvisited">未訪問</span>'}
                    </div>
                    <div class="mobile-item-name-jp">${company.nameJp}</div>
                    <div class="mobile-item-industry">${DataUtils.getIndustryDisplayName(company.industry)}</div>
                    <div class="mobile-item-address">${company.district} - ${company.address}</div>
                    ${isVisited ? this.generateMemoSummary(company.companyMemoList) : ''}
                </div>
            `;
        }).join('');

        this.mobileListContent.innerHTML = html;

        // 重新绑定事件
        this.bindMobileListEvents();

        // 保持当前选中状态
        if (this.selectedCompanyId) {
            const selectedItem = document.querySelector(`[data-company-id="${this.selectedCompanyId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
            }
        }
    }

    bindMobileListEvents() {
        document.querySelectorAll('.mobile-company-item').forEach(item => {
            item.addEventListener('click', () => {
                const companyId = parseInt(item.dataset.companyId);
                this.selectCompany(companyId);
                this.hideCompanyList(); // 选择后自动隐藏列表
            });
        });
    }
    
    selectCompany(companyId) {
        // 更新选中状态
        this.selectedCompanyId = companyId;
        
        // 更新列表中的选中状态
        document.querySelectorAll('.mobile-company-item').forEach(item => {
            item.classList.remove('active');
            if (parseInt(item.dataset.companyId) === companyId) {
                item.classList.add('active');
            }
        });
        
        // 获取公司信息
        const company = DataUtils.getCompanyById(companyId);
        if (!company) return;
        
        // 移动地图到公司位置
        this.map.setCenter(company.coordinates);
        if (this.currentZoom < 14) {
            this.map.setZoom(15);
        }
        
        // 更新标记状态
        this.updateMarkerStates();
    }
    
    updateMarkerStates() {
        // 更新所有标记的状态
        this.markers.forEach(marker => {
            const extData = marker.getExtData();

            // 处理主标记
            if (typeof extData === 'number') {
                const companyId = extData;
                const company = DataUtils.getCompanyById(companyId);
                const isVisited = company ? this.isCompanyVisited(company) : false;

                let markerClass = 'custom-marker';
                if (isVisited) {
                    markerClass += ' visited';
                } else {
                    markerClass += ' unvisited';
                }

                if (companyId === this.selectedCompanyId) {
                    markerClass += ' active';
                }

                marker.setContent(`<div class="${markerClass}"></div>`);
            }
        });
    }

    updateMarkersAndClusters() {
        if (!this.map) return;

        // 清除现有标记和聚合
        this.clearMarkersAndClusters();

        // 根据缩放级别决定显示聚合还是单个标记
        if (this.currentZoom < 14) {
            this.showClusters();
        } else {
            this.showIndividualMarkers();
        }
    }

    clearMarkersAndClusters() {
        // 移除现有标记
        this.markers.forEach(marker => {
            this.map.remove(marker);
        });
        this.markers = [];

        // 移除现有聚合
        this.clusters.forEach(cluster => {
            this.map.remove(cluster);
        });
        this.clusters = [];
    }

    showClusters() {
        // 按区域分组公司
        const districts = {};
        this.filteredCompanies.forEach(company => {
            const district = company.district;
            if (!districts[district]) {
                districts[district] = [];
            }
            districts[district].push(company);
        });

        // 创建聚合标记
        Object.entries(districts).forEach(([district, companies]) => {
            if (companies.length === 0) return;

            // 计算该区域内公司的中心点
            const centerLat = companies.reduce((sum, c) => sum + c.coordinates[1], 0) / companies.length;
            const centerLng = companies.reduce((sum, c) => sum + c.coordinates[0], 0) / companies.length;

            // 创建聚合标记
            const clusterMarker = this.createClusterMarker([centerLng, centerLat], companies, district);
            this.map.add(clusterMarker);
            this.clusters.push(clusterMarker);
        });
    }

    createClusterMarker(coordinates, companies, district) {
        const count = companies.length;
        let size = 'small';
        if (count > 10) size = 'large';
        else if (count > 5) size = 'medium';

        const marker = new AMap.Marker({
            position: coordinates,
            content: `<div class="cluster-marker ${size}">${count}</div>`,
            offset: new AMap.Pixel(-25, -25)
        });

        // 添加点击事件
        marker.on('click', () => {
            this.showClusterDetails(district, companies);
        });

        return marker;
    }

    showIndividualMarkers() {
        this.filteredCompanies.forEach(company => {
            const marker = this.createCompanyMarker(company);
            this.map.add(marker);
            this.markers.push(marker);
        });
    }

    createCompanyMarker(company) {
        // 判断公司是否被拜访过
        const isVisited = this.isCompanyVisited(company);

        // 创建标记点（根据拜访状态设置不同样式）
        const markerContent = `<div class="custom-marker ${isVisited ? 'visited' : 'unvisited'}"></div>`;

        const marker = new AMap.Marker({
            position: company.coordinates,
            content: markerContent,
            anchor: 'center', // 设置锚点为中心，避免缩放时偏移
            extData: company.id // 存储公司ID
        });

        // 创建信息标签（独立的标记，紧贴公司标记）
        const labelContent = `
            <div class="marker-label">
                <div class="marker-label-name">${company.name}</div>
                <div class="marker-label-industry">${DataUtils.getIndustryDisplayName(company.industry)}</div>
                <div class="marker-label-address">${company.district}</div>
            </div>
        `;

        const labelMarker = new AMap.Marker({
            position: company.coordinates,
            content: labelContent,
            anchor: 'bottom-center', // 标签底部中心对齐标记点
            offset: new AMap.Pixel(0, -30), // 向上偏移，避免重叠
            extData: `label_${company.id}`,
            zIndex: 100 // 确保标签在标记点之上
        });

        // 添加点击事件到主标记
        marker.on('click', () => {
            this.selectCompany(company.id);
        });

        // 添加双击事件显示详细信息
        marker.on('dblclick', () => {
            this.showCompanyDetails(company);
        });

        // 标签也可以点击
        labelMarker.on('click', () => {
            this.selectCompany(company.id);
        });

        // 将标签标记也添加到地图
        this.map.add(labelMarker);
        this.markers.push(labelMarker); // 同时管理标签标记

        return marker;
    }

    showCompanyDetails(company) {
        this.closeClusterPanel();

        // 加载公司备注
        this.loadCompanyNotes();

        // 填充面板内容，包含备注编辑功能
        this.panelContent.innerHTML = `
            <div class="company-detail">
                <h4>${company.name}</h4>
                <div class="company-detail-jp">日本社名: ${company.nameJp}</div>
                <div class="company-industry-tag">${DataUtils.getIndustryDisplayName(company.industry)}</div>

                <div class="detail-row">
                    <span class="detail-label">住所:</span>
                    <span class="detail-value">${company.address}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">電話:</span>
                    <span class="detail-value">${company.phone}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">従業員数:</span>
                    <span class="detail-value">${company.employees}人</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">設立年:</span>
                    <span class="detail-value">${company.established}年</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">ウェブサイト:</span>
                    <span class="detail-value"><a href="${company.website}" target="_blank">${company.website}</a></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">備考:</span>
                    <span class="detail-value" id="notes-value">${company.notes || '備考なし'}</span>
                    <button class="btn btn-outline btn-small edit-notes-btn" style="margin-left: 10px;">編集</button>
                </div>
                <div class="detail-row" id="edit-notes-section" style="display: none;">
                    <span class="detail-label">備考編集:</span>
                    <div style="flex: 1;">
                        <textarea id="notes-textarea" class="notes-textarea" rows="3" placeholder="ここに備考を入力してください">${company.notes || ''}</textarea>
                        <div style="margin-top: 10px;">
                            <button class="btn btn-primary save-notes-btn">保存</button>
                            <button class="btn btn-secondary cancel-notes-btn" style="margin-left: 10px;">キャンセル</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定备注编辑功能
        this.bindNotesEditingEvents(company);

        // 显示面板
        this.companyPanel.classList.add('show');
        document.querySelector('.map-overlay').classList.add('show');
    }

    showClusterDetails(district, companies) {
        this.closeCompanyPanel();

        // 填充聚合面板内容
        this.clusterContent.innerHTML = `
            <div class="cluster-summary">
                <div class="cluster-count">${companies.length}</div>
                <div class="cluster-area">${district}の会社</div>
            </div>
            <div class="cluster-companies">
                ${companies.map(company => `
                    <div class="cluster-company-item" data-company-id="${company.id}">
                        <div class="cluster-company-name">${company.name}</div>
                        <div class="cluster-company-address">${company.address}</div>
                    </div>
                `).join('')}
            </div>
        `;

        // 绑定点击事件
        this.clusterContent.querySelectorAll('.cluster-company-item').forEach(item => {
            item.addEventListener('click', () => {
                const companyId = item.dataset.companyId;
                const company = DataUtils.getCompanyById(companyId);
                if (company) {
                    this.closeClusterPanel();
                    this.map.setCenter(company.coordinates);
                    this.map.setZoom(15);
                    setTimeout(() => this.showCompanyDetails(company), 500);
                }
            });
        });

        // 显示聚合面板
        this.clusterPanel.classList.add('show');
        document.querySelector('.map-overlay').classList.add('show');
    }

    closeCompanyPanel() {
        this.companyPanel.classList.remove('show');
        document.querySelector('.map-overlay').classList.remove('show');
    }

    closeClusterPanel() {
        this.clusterPanel.classList.remove('show');
        document.querySelector('.map-overlay').classList.remove('show');
    }

    bindNotesEditingEvents(company) {
        const editNotesBtn = this.panelContent.querySelector('.edit-notes-btn');
        const editNotesSection = this.panelContent.querySelector('#edit-notes-section');
        const notesValue = this.panelContent.querySelector('#notes-value');
        const notesTextarea = this.panelContent.querySelector('#notes-textarea');

        // Edit notes button event
        editNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            notesValue.style.display = 'none';
            editNotesBtn.style.display = 'none';
            editNotesSection.style.display = 'flex';
        });

        // Cancel notes button event
        const cancelNotesBtn = this.panelContent.querySelector('.cancel-notes-btn');
        cancelNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            notesTextarea.value = company.notes || '';
            notesValue.style.display = 'block';
            editNotesBtn.style.display = 'inline-block';
            editNotesSection.style.display = 'none';
        });

        // Save notes button event
        const saveNotesBtn = this.panelContent.querySelector('.save-notes-btn');
        saveNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const newNotes = notesTextarea.value;
            company.notes = newNotes;

            // Update the displayed notes
            notesValue.textContent = newNotes || '備考なし';
            notesValue.style.display = 'block';
            editNotesBtn.style.display = 'inline-block';
            editNotesSection.style.display = 'none';

            // Save to localStorage and update company data
            this.saveCompanyNotes(company, { memo: newNotes });

            // 实时更新所有相关UI组件
            this.updateCompanyListDisplay();
            this.updateMarkersAndClusters();
            this.updateMarkerStates();
        });
    }

    saveCompanyNotes(company, data) {
        // 更新公司的companyMemoList
        if (!company.companyMemoList) {
            company.companyMemoList = [];
        }

        // 添加新的备注到列表
        const newMemo = {
            id: Date.now(), // 临时ID
            userId: localStorage.getItem('userId') || 'user',
            memo: data.memo,
            createTime: new Date().toISOString()
        };

        company.companyMemoList.push(newMemo);

        // 保存到localStorage作为备份
        let companyNotes = JSON.parse(localStorage.getItem('companyNotes') || '{}');
        companyNotes[company.id] = data.memo;
        localStorage.setItem('companyNotes', JSON.stringify(companyNotes));

        // 发送到服务器
        const requestData = {
            id: company.notes == '' ? '' : company.memoId,
            memo: data.memo,
            companyId: company.id
        };

        fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/companyMemo/insertOrUpdate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Authorization': 'Bearer ' + localStorage.getItem('authToken')
            },
            body: JSON.stringify(requestData)
        }).then(response => response.json())
        .then(data => {
            console.log('备注保存成功:', data);
        })
        .catch(error => {
            console.error('备注保存失败:', error);
        });
    }

    loadCompanyNotes() {
        // Load notes from localStorage
        let companyNotes = JSON.parse(localStorage.getItem('companyNotes') || '{}');

        // Apply notes to all companies
        DataUtils.getAllCompanies().forEach(company => {
            if (companyNotes[company.id]) {
                company.notes = companyNotes[company.id];
            }
        });

        // Also apply to filtered companies
        this.filteredCompanies.forEach(company => {
            if (companyNotes[company.id]) {
                company.notes = companyNotes[company.id];
            }
        });
    }

    // 判断公司是否被拜访过（根据companyMemoList长度）
    isCompanyVisited(company) {
        return company.companyMemoList && company.companyMemoList.length > 0;
    }

    generateMemoSummary(memoList) {
        if (!memoList || memoList.length === 0) return '';

        const latestMemo = memoList[memoList.length - 1]; // 获取最新的备注
        return `
            <div class="mobile-item-notes">
                <div class="memo-count">備考: ${memoList.length}件</div>
                <div class="memo-latest">最新: ${latestMemo.content || latestMemo.memo || '内容なし'}</div>
            </div>
        `;
    }
}

// 初始化移动端地图功能
document.addEventListener('DOMContentLoaded', () => {
    new MobileCompanyMap();
});
