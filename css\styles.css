/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    width: 100%;
    padding: 0;
}

/* Header */
.header {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
    height: 4rem;
    box-sizing: border-box;
}

.logo {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.nav {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #3498db;
    /* background-color: #f8f9fa; */
}
.hide-scrollbar-1::-webkit-scrollbar {
    display: none;
}
.hide-scrollbar-1 {
    -ms-overflow-style: none;  /* IE/Edge */
    scrollbar-width: none;  /* Firefox */
}
/* 用户导航区域 */
.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: auto;
}

.user-name {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

.logout-btn {
    background-color: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

/* Main Content */
.main {
    min-height: calc(100vh - 120px);
    padding: 0;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid #3498db;
    color: #3498db;
}

.btn-outline:hover {
    background-color: #3498db;
    color: white;
}

/* Form Elements */
input[type="text"],
input[type="email"],
input[type="tel"],
select,
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

/* Cards */
.card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.card-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.card-subtitle {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        gap: 1rem;
    }
    
    .container {
        padding: 0 15px;
    }
    
    .main {
        padding: 1rem 0;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-block { display: inline-block; }

.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error and Success Messages */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* 公司列表拜访状态样式 */
.company-name-wrapper {
    display: grid;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
    grid-template-columns: 1fr auto;
}

.company-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* 拜访状态标签样式 */
.visit-status {
    font-size: 0.75rem;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-weight: 500;
    white-space: nowrap;
    margin-bottom: 5px;
}

.visit-status.visited {
    background-color: #d4edda;
    color: #155724;
}

.visit-status.unvisited {
    background-color: #f8d7da;
    color: #721c24;
}

/* 公司项目拜访状态样式 */
.company-item.visited {
    border-left: 4px solid #28a745;
    background-color: #f8fff9;
}

.company-item.unvisited {
    border-left: 4px solid #dc3545;
    background-color: #fff5f5;
}

.company-item.visited:hover {
    background-color: #e8f5e8;
}

.company-item.unvisited:hover {
    background-color: #ffeaea;
}

/* 备注摘要样式 */
.memo-summary {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.memo-count {
    font-size: 0.8rem;
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.25rem;
}

.memo-latest {
    font-size: 0.75rem;
    color: #666;
    line-height: 1.3;
    max-height: 2.6em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Radio Button 组样式 */
.field-label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    /* margin-bottom: 1rem; */
    padding: 0.5rem 0;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.6rem 1rem;
    /* border: 1px solid #e0e0e0; */
    border-radius: 8px;
    /* background-color: #fff; */
    transition: all 0.2s ease;
    font-size: 0.9rem;
    width: 85px;
    justify-content: center;
    /* box-shadow: 0 1px 3px rgba(0,0,0,0.1); */
}

.radio-option:hover {
    /* border-color: #3498db; */
    /* background-color: #f8f9fa; */
    transform: translateY(-1px);
    color: #3498db;;
    /* box-shadow: 0 2px 6px rgba(0,0,0,0.15); */
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-option input[type="radio"]:checked + .radio-text {
    color: #3498db;;
}

.radio-option:has(input[type="radio"]:checked) {
    /* background-color: #3498db; */
    /* border-color: #3498db; */
    color: #3498db;;
    /* box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3); */
}

.radio-text {
    font-weight: 500;
    transition: color 0.2s ease;
    white-space: nowrap;
}

/* 特殊样式：制限なし选项 */
.radio-option:has(input[value=""]) {
    /* background-color: #f8f9fa; */
    /* border-color: #dee2e6; */
    /* color: #3498db;; */
}

.radio-option:has(input[value=""]):hover {
    /* background-color: #e9ecef; */
    /* border-color: #adb5bd; */
    transform: translateY(-1px);
    color: #3498db;;
}

.radio-option:has(input[value=""]:checked) {
    /* background-color: #6c757d; */
    /* border-color: #6c757d; */
    color: #3498db;;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .radio-group {
        gap: 0.5rem;
    }

    .radio-option {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
        min-width: 75px;
    }
}

@media (max-width: 480px) {
    .radio-group {
        gap: 0.4rem;
    }

    .radio-option {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
        min-width: 65px;
    }
}
