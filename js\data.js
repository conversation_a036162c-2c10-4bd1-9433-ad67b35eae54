// Sample company data for the search system
let companiesData = [
    {
        id: 1,
        name: "杭州現代化スパンデックス有限公司",
        nameJp: "旭化成",
        industry: "manufacturing",
        industryJp: "化学",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市萧山区技術开发区MTO-5-2",
        coordinates: [120.2619, 30.2741],
        district: "萧山区",
        phone: "+86-571-8234-5678",
        website: "https://example.com",
        employees: "500-1000",
        established: "1995"
    },
    {
        id: 2,
        name: "東京海上日動火災保険（中国）有限公司 浙江分公司",
        nameJp: "東京海上ホールディングス",
        industry: "finance",
        industryJp: "保険業",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市上城区钱江新城1号楼1405号",
        coordinates: [120.2103, 30.2467],
        district: "上城区",
        phone: "+86-571-8765-4321",
        website: "https://example.com",
        employees: "100-500",
        established: "2008"
    },
    {
        id: 3,
        name: "杭州善多見貿易有限公司",
        nameJp: "株式会社オーアンドエム工芸",
        industry: "retail",
        industryJp: "貿易業",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市蕭山区钱塘新区银河街道银河119号杭州湾数智制造产业园5号楼3楼310室",
        coordinates: [120.3619, 30.1741],
        district: "萧山区",
        phone: "+86-571-5678-9012",
        website: "https://example.com",
        employees: "50-100",
        established: "2010"
    },
    {
        id: 4,
        name: "三井住友銀行（中国）有限公司 杭州支店",
        nameJp: "三井住友銀行",
        industry: "finance",
        industryJp: "銀行業",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "5F, Offices At Kerry Centre, 385 Yan An Road, Gong Shu District, Hangzhou, Zhejiang Province, The People's Republic of China",
        coordinates: [120.1503, 30.2867],
        district: "拱墅区",
        phone: "+86-571-8888-9999",
        website: "https://example.com",
        employees: "100-500",
        established: "2005"
    },
    {
        id: 5,
        name: "ソニー（中国）有限公司 杭州分公司",
        nameJp: "ソニー株式会社",
        industry: "technology",
        industryJp: "電子機器",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市西湖区文三路259号昌地火炬大厦2号楼15楼",
        coordinates: [120.1203, 30.2667],
        district: "西湖区",
        phone: "+86-571-8777-6666",
        website: "https://example.com",
        employees: "200-500",
        established: "2000"
    },
    {
        id: 6,
        name: "パナソニック（中国）有限公司 杭州分公司",
        nameJp: "パナソニック株式会社",
        industry: "technology",
        industryJp: "電子機器",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市滨江区江南大道3688号潮人汇大厦A座20楼",
        coordinates: [120.2103, 30.2067],
        district: "滨江区",
        phone: "+86-571-8666-5555",
        website: "https://example.com",
        employees: "300-500",
        established: "1998"
    },
    {
        id: 7,
        name: "トヨタ自動車（中国）投資有限公司 杭州事務所",
        nameJp: "トヨタ自動車株式会社",
        industry: "manufacturing",
        industryJp: "自動車",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市钱塘区钱江路1366号华润大厦A座28楼",
        coordinates: [120.2403, 30.2567],
        district: "钱塘区",
        phone: "+86-571-8555-4444",
        website: "https://example.com",
        employees: "100-200",
        established: "2002"
    },
    {
        id: 8,
        name: "日立（中国）有限公司 杭州分公司",
        nameJp: "株式会社日立製作所",
        industry: "technology",
        industryJp: "総合電機",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市拱墅区莫干山路972号泰嘉园D座15楼",
        coordinates: [120.1403, 30.3067],
        district: "拱墅区",
        phone: "+86-571-8444-3333",
        website: "https://example.com",
        employees: "150-300",
        established: "2001"
    },
    {
        id: 9,
        name: "キヤノン（中国）有限公司 杭州分公司",
        nameJp: "キヤノン株式会社",
        industry: "technology",
        industryJp: "精密機器",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市西湖区教工路18号世贸丽晶城欧美中心A座1201室",
        coordinates: [120.1303, 30.2767],
        district: "西湖区",
        phone: "+86-571-8333-2222",
        website: "https://example.com",
        employees: "80-150",
        established: "2003"
    },
    {
        id: 10,
        name: "富士通（中国）信息系统有限公司 杭州分公司",
        nameJp: "富士通株式会社",
        industry: "technology",
        industryJp: "情報通信",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市臨平区体育场路288号建銀中心1801室",
        coordinates: [120.1703, 30.2867],
        district: "临平区",
        phone: "+86-571-8222-1111",
        website: "https://example.com",
        employees: "120-250",
        established: "2004"
    },
    {
        id: 11,
        name: "伊藤忠商事（中国）集团有限公司 杭州代表处",
        nameJp: "伊藤忠商事株式会社",
        industry: "retail",
        industryJp: "総合商社",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市上城区平海路58号平海大厦6楼",
        coordinates: [120.1603, 30.2567],
        district: "上城区",
        phone: "+86-571-8111-0000",
        website: "https://example.com",
        employees: "50-100",
        established: "2006"
    },
    {
        id: 12,
        name: "住友商事（中国）有限公司 杭州代表处",
        nameJp: "住友商事株式会社",
        industry: "retail",
        industryJp: "総合商社",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市西湖区文二路391号西湖国际科技大厦D座1502室",
        coordinates: [120.1103, 30.2967],
        district: "西湖区",
        phone: "+86-571-8000-9999",
        website: "https://example.com",
        employees: "30-80",
        established: "2007"
    },
    {
        id: 13,
        name: "丸紅（中国）有限公司 杭州代表处",
        nameJp: "丸紅株式会社",
        industry: "retail",
        industryJp: "総合商社",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市钱塘区钱江路1366号华润大厦B座25楼",
        coordinates: [120.2503, 30.2467],
        district: "钱塘区",
        phone: "+86-571-8999-8888",
        website: "https://example.com",
        employees: "40-90",
        established: "2009"
    },
    {
        id: 14,
        name: "双日（中国）有限公司 杭州代表处",
        nameJp: "双日株式会社",
        industry: "retail",
        industryJp: "総合商社",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市臨平区体育场路288号建銀中心2201室",
        coordinates: [120.1803, 30.2767],
        district: "临平区",
        phone: "+86-571-8888-7777",
        website: "https://example.com",
        employees: "25-60",
        established: "2011"
    },
    {
        id: 15,
        name: "豊田通商（中国）有限公司 杭州代表处",
        nameJp: "豊田通商株式会社",
        industry: "manufacturing",
        industryJp: "商社・自動車関連",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市滨江区江南大道3688号潮人汇大厦B座18楼",
        coordinates: [120.2203, 30.2167],
        district: "滨江区",
        phone: "+86-571-8777-6666",
        website: "https://example.com",
        employees: "60-120",
        established: "2003"
    },
    {
        id: 16,
        name: "NEC（中国）有限公司 杭州分公司",
        nameJp: "日本電気株式会社",
        industry: "technology",
        industryJp: "情報通信",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市拱墅区莫干山路972号泰嘉园E座12楼",
        coordinates: [120.1503, 30.3167],
        district: "拱墅区",
        phone: "+86-571-8666-5555",
        website: "https://example.com",
        employees: "90-180",
        established: "2002"
    },
    {
        id: 17,
        name: "オムロン（中国）有限公司 杭州分公司",
        nameJp: "オムロン株式会社",
        industry: "technology",
        industryJp: "電子部品",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市西湖区教工路18号世贸丽晶城欧美中心B座1501室",
        coordinates: [120.1403, 30.2867],
        district: "西湖区",
        phone: "+86-571-8555-4444",
        website: "https://example.com",
        employees: "70-140",
        established: "2004"
    },
    {
        id: 18,
        name: "村田制作所（中国）投資有限公司 杭州代表处",
        nameJp: "株式会社村田製作所",
        industry: "technology",
        industryJp: "電子部品",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市蕭山区钱塘新区银河街道银河200号杭州湾数智制造产业园8号楼2楼",
        coordinates: [120.3719, 30.1841],
        district: "萧山区",
        phone: "+86-571-8444-3333",
        website: "https://example.com",
        employees: "100-200",
        established: "2005"
    },
    {
        id: 19,
        name: "京セラ（中国）商贸有限公司 杭州代表处",
        nameJp: "京セラ株式会社",
        industry: "technology",
        industryJp: "精密機器",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市上城区平海路58号平海大厦8楼",
        coordinates: [120.1703, 30.2467],
        district: "上城区",
        phone: "+86-571-8333-2222",
        website: "https://example.com",
        employees: "50-100",
        established: "2006"
    },
    {
        id: 20,
        name: "ダイキン工業（中国）投資有限公司 杭州分公司",
        nameJp: "ダイキン工業株式会社",
        industry: "manufacturing",
        industryJp: "空調機器",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市钱塘区钱江路1366号华润大厦C座22楼",
        coordinates: [120.2603, 30.2367],
        district: "钱塘区",
        phone: "+86-571-8222-1111",
        website: "https://example.com",
        employees: "80-160",
        established: "2001"
    },
    {
        id: 21,
        name: "コマツ（中国）投資有限公司 杭州代表处",
        nameJp: "株式会社小松製作所",
        industry: "manufacturing",
        industryJp: "建設機械",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市滨江区江南大道3688号潮人汇大厦C座16楼",
        coordinates: [120.2303, 30.2067],
        district: "滨江区",
        phone: "+86-571-8111-0000",
        website: "https://example.com",
        employees: "60-120",
        established: "2000"
    },
    {
        id: 22,
        name: "クボタ（中国）投資有限公司 杭州代表处",
        nameJp: "株式会社クボタ",
        industry: "manufacturing",
        industryJp: "農業機械",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市拱墅区莫干山路972号泰嘉园F座10楼",
        coordinates: [120.1603, 30.3267],
        district: "拱墅区",
        phone: "+86-571-8000-9999",
        website: "https://example.com",
        employees: "40-80",
        established: "2008"
    },
    {
        id: 23,
        name: "ヤマハ発動機（中国）有限公司 杭州代表处",
        nameJp: "ヤマハ発動機株式会社",
        industry: "manufacturing",
        industryJp: "輸送機器",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市西湖区文三路259号昌地火炬大厦3号楼12楼",
        coordinates: [120.1303, 30.2567],
        district: "西湖区",
        phone: "+86-571-7999-8888",
        website: "https://example.com",
        employees: "50-100",
        established: "2007"
    },
    {
        id: 24,
        name: "資生堂（中国）投資有限公司 杭州分公司",
        nameJp: "株式会社資生堂",
        industry: "retail",
        industryJp: "化粧品",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市臨平区体育场路288号建銀中心1901室",
        coordinates: [120.1903, 30.2967],
        district: "临平区",
        phone: "+86-571-7888-7777",
        website: "https://example.com",
        employees: "30-70",
        established: "2010"
    },
    {
        id: 25,
        name: "花王（中国）投資有限公司 杭州代表处",
        nameJp: "花王株式会社",
        industry: "retail",
        industryJp: "日用品",
        prefecture: "zhejiang",
        city: "杭州市",
        address: "中国浙江省杭州市蕭山区钱塘新区银河街道银河300号杭州湾数智制造产业园10号楼3楼",
        coordinates: [120.3819, 30.1941],
        district: "萧山区",
        phone: "+86-571-7777-6666",
        website: "https://example.com",
        employees: "40-90",
        established: "2009"
    }
];

// Industry mapping for Japanese display
const industryMapping = {
    manufacturing: "製造業",
    finance: "金融業",
    technology: "IT・技術",
    retail: "小売業",
    healthcare: "医療・健康",
    education: "教育",
    construction: "建設業",
    logistics: "物流"
};

// Prefecture mapping
const prefectureMapping = {
    zhejiang: "浙江省"
};

// Utility functions for data manipulation
const DataUtils = {
    // Get all companies
    getAllCompanies: () => DataUtils.fetchCompanyData(),
    
    // Get company by ID
    getCompanyById: (id) => companiesData.find(company => company.id === parseInt(id)),
    
    // Search companies by various criteria
    searchCompanies: (criteria) => {
        return companiesData.filter(company => {
            const matchesName = !criteria.name || 
                company.name.toLowerCase().includes(criteria.name.toLowerCase()) ||
                company.nameJp.toLowerCase().includes(criteria.name.toLowerCase()) ||
                company.industry.toLowerCase().includes(criteria.name.toLowerCase()) ||
                company.industryJp.toLowerCase().includes(criteria.name.toLowerCase()) ||
                company.address.toLowerCase().includes(criteria.name.toLowerCase());
            
            const matchesIndustry = !criteria.industry || company.industry === criteria.industry;
            
            const matchesPrefecture = !criteria.prefecture || company.city === criteria.prefecture;
            
            const matchesCity = !criteria.city || 
                company.district.toLowerCase().includes(criteria.city.toLowerCase());
            
            return matchesName && matchesIndustry && matchesPrefecture && matchesCity;
        });
    },
    
    // Get companies by district for clustering
    getCompaniesByDistrict: () => {
        const districts = {};
        companiesData.forEach(company => {
            const district = company.district;
            if (!districts[district]) {
                districts[district] = [];
            }
            districts[district].push(company);
        });
        return districts;
    },
    
    // Get industry display name
    getIndustryDisplayName: (industry) => industryMapping[industry] || industry,
    
    // Get prefecture display name
    getPrefectureDisplayName: (prefecture) => prefectureMapping[prefecture] || prefecture,
    
    // Sort companies
    sortCompanies: (companies, sortBy) => {
        const sorted = [...companies];
        switch (sortBy) {
            case 'name':
                return sorted.sort((a, b) => a.name.localeCompare(b.name));
            case 'industry':
                return sorted.sort((a, b) => a.industry.localeCompare(b.industry));
            case 'location':
                return sorted.sort((a, b) => {
                    const locationA = `${a.prefecture}-${a.city}-${a.district}`;
                    const locationB = `${b.prefecture}-${b.city}-${b.district}`;
                    return locationA.localeCompare(locationB);
                });
            default:
                return sorted;
        }
    },

   // 修改 fetchCompanyData 函数
    fetchCompanyData: async () => {
        console.log('Fetching company data...');
        try {
            // 始终从服务器获取最新数据
            const response = await fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/company/queryCompanyInfoList', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Authorization': 'Bearer ' + localStorage.getItem('authToken')
                }
            });

            // 检查响应状态
            if (!response.ok) {
                if (response.status === 403) {
                    console.error('403 Forbidden: Access denied to company data API');
                    alert('アクセスが拒否されました。ログインし直してください。');
                    // 清除本地存储的认证信息
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userId');
                    localStorage.removeItem('realName');
                    // 重定向到登录页面
                    window.location.href = 'login.html';
                    return []; // 返回空数组
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // 处理数据
            data.data.forEach(company => {
                let floatArr;
                if (company.geographicalCoordinates != '') {
                    floatArr = company.geographicalCoordinates.split(',').map(str => {
                        const num = parseFloat(str);
                        return isNaN(num) ? [120.125623, 30.158845] : num; // 默认杭州坐标
                    });
                } else {
                    floatArr = [120.125623, 30.158845]; // 默认杭州坐标
                }
                
                company.name = company.companyName;
                company.nameJp = company.japanCompanyName;
                company.industry = company.businessType;
                company.industryJp = company.businessType;
                company.prefecture = company.province;
                company.city = company.city;
                company.address = company.location;
                company.coordinates = floatArr;
                company.district = company.district;
                company.phone = "";
                company.website = "";
                company.employees = "";
                company.established = "";
            });
            
            console.log('Company data:', data.data);
            companiesData = data.data;
            return data.data;
            
        } catch (error) {
            console.error('Error fetching company data:', error);
             // 如果是 403 错误，已经在上面处理过了，这里不需要重复处理
            if (error.message && error.message.includes('403')) {
                return [];
            }
            
            // 显示其他错误信息给用户
            // alert(`获取公司数据失败: ${error.message}`);
            return [];
        }
    }
    
};
