// Search page functionality
class CompanySearch {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.currentResults = [];
        this.currentSort = '';
        this.currentFilter = '';
        
        this.initializeElements();
        this.bindEvents();
        this.displayInitialState();
        // this.loadCompanyNotes(); // Load company notes when initializing
    }
    
    initializeElements() {
        // Search form elements
        this.companyNameInput = document.getElementById('company-name');
        this.industrySelect = document.getElementById('industry');

        // Radio button groups
        this.prefectureRadios = document.querySelectorAll('input[name="prefecture"]');
        this.cityRadios = document.querySelectorAll('input[name="city"]');
        this.city1Group = document.getElementById('city1-group');
        this.city2Group = document.getElementById('city2-group');
        this.areaGroup = document.getElementById('area-row');

        this.searchBtn = document.getElementById('search-btn');
        this.clearBtn = document.getElementById('clear-btn');

        // Results elements
        this.resultsCount = document.getElementById('results-count');
        this.sortSelect = document.getElementById('sort-by');
        this.filterSelect = document.getElementById('filter-by');
        this.resultsContainer = document.getElementById('results-container');
        this.paginationContainer = document.getElementById('pagination');
        this.scrollTag = document.getElementById('scroll-tag');
    }
    
    bindEvents() {
        // Search button click
        this.searchBtn.addEventListener('click', () => this.performSearch());
        
        // Clear button click
        this.clearBtn.addEventListener('click', () => this.clearSearch());
        
        // Enter key in search fields
        [this.companyNameInput].forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        });
        this.companyNameInput.addEventListener('input', (e) => {
            this.performSearch();
        });
        
        // Sort change
        this.sortSelect.addEventListener('change', () => {
            this.currentSort = this.sortSelect.value;
            this.sortAndDisplayResults();
        });
        // Fliter change
        this.filterSelect.addEventListener('change', () => {
            this.currentFilter = this.filterSelect.value;
            this.sortAndDisplayResults();
        });
        // Auto-search on dropdown changes
        [this.industrySelect].forEach(select => {
            select.addEventListener('change', () => {
                if (this.hasSearchCriteria()) {
                    this.performSearch();
                }
            });
        });
        // Auto-search on radio button changes
        this.prefectureRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.handlePrefectureChange();
                this.performSearch();
            });
        });

        this.cityRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                if (this.hasSearchCriteria()) {
                    this.performSearch();
                }
            });
        });
        DataUtils.fetchCompanyData().then(companiesData => {
            this.performSearch()
        });
    }

    handlePrefectureChange() {
        const selectedPrefecture = this.getSelectedPrefecture();

        // 重置城市选择
        this.cityRadios.forEach(radio => {
            radio.checked = radio.value === '';
        });

        // 显示对应的城市选项组
        if (selectedPrefecture === '杭州市') {
            this.areaGroup.style.display ='block'
            this.city1Group.style.display = 'flex';
            this.city2Group.style.display = 'none';
        } else if (selectedPrefecture === '青岛市') {
            this.areaGroup.style.display ='block'
            this.city1Group.style.display = 'none';
            this.city2Group.style.display = 'flex';
        } else {
            this.areaGroup.style.display ='none'
            this.city1Group.style.display = 'none';
            this.city2Group.style.display = 'none';
        }
    }

    getSelectedPrefecture() {
        const selectedRadio = document.querySelector('input[name="prefecture"]:checked');
        return selectedRadio ? selectedRadio.value : '';
    }

    getSelectedCity() {
        const selectedRadio = document.querySelector('input[name="city"]:checked');
        return selectedRadio ? selectedRadio.value : '';
    }

    hasSearchCriteria() {
        return this.companyNameInput.value.trim() ||
               this.industrySelect.value ||
               this.getSelectedPrefecture() ||
               this.getSelectedCity();
    }
    
    displayInitialState() {
        this.resultsCount.textContent = '検索条件を入力して検索してください';
        this.resultsContainer.innerHTML = this.getEmptyStateHTML();
    }
    
    performSearch() {
        // Show loading state
        this.showLoadingState();
        
        // Get search criteria
        const criteria = {
            name: this.companyNameInput.value.trim(),
            industry: this.industrySelect.value,
            prefecture: this.getSelectedPrefecture(),
            city: this.getSelectedCity()
        };
        
        // Simulate API delay
        setTimeout(() => {
            // Perform search
            this.currentResults = DataUtils.searchCompanies(criteria);
            this.currentPage = 1;
            
            // Sort results
            this.sortAndDisplayResults();
        }, 500);
    }
    
    sortAndDisplayResults() {
        // Sort results
        const sortedResults = DataUtils.sortCompanies(this.currentResults, this.currentSort);
        const filteredResults = DataUtils.filterCompanies(sortedResults, this.currentFilter);
        
        // Update results count
        this.updateResultsCount(filteredResults.length);
        
        // Display results
        this.displayResults(filteredResults);
        
        // Update pagination
        this.updatePagination(filteredResults.length);
    }
    
    showLoadingState() {
        this.resultsCount.textContent = '検索中...';
        this.resultsContainer.innerHTML = `
            <div class="loading-state">
                <div class="loading"></div>
                <p>検索中です...</p>
            </div>
        `;
    }
    
    updateResultsCount(total) {
        if (total === 0) {
            this.resultsCount.textContent = '検索結果が見つかりませんでした';
        } else {
            this.resultsCount.textContent = `${total}社の杭州商社をお見つけしました`;
        }
    }
    
    displayResults(results) {
        if (results.length === 0) {
            this.resultsContainer.innerHTML = this.getEmptyStateHTML();
            return;
        }
        
        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageResults = results.slice(startIndex, endIndex);
        
        // Generate HTML
        const html = pageResults.map(company => this.generateCompanyHTML(company)).join('');
        this.resultsContainer.innerHTML = html;
        
        // Bind click events for company items
        this.bindCompanyItemEvents();

        // 绑定访问状态选择框事件
        this.bindVisitStatusEvents();
    }
    
    generateCompanyHTML(company) {
        // 判断是否已拜访（根据company.memo的值是否为visited）
        const isVisited = (company.memo == 'visited');

        return `
            <div class="company-item ${isVisited ? 'visited' : 'unvisited'}" data-company-id="${company.id}">
                <div class="company-header">
                    <div>
                        <div class="company-name-wrapper">
                            <div class="company-name">${company.name}</div>
                        </div>
                        <div class="company-name-jp">日本社名: ${company.nameJp}</div>
                    </div>
                    <div>
                        <select class="visit-status-select" data-company-id="${company.id}">
                            <option value="unvisited" ${!isVisited ? 'selected' : ''}>未訪問</option>
                            <option value="visited" ${isVisited ? 'selected' : ''}>訪問済み</option>
                        </select>
                        ${company.industry == '' ? '' : `<div class="company-industry">${company.industry}</div>`}
                    </div>
                    
                </div>
                <div class="company-details">
                    <div class="company-info">
                        <div class="company-address">${company.address}</div>
                        ${isVisited ? this.generateMemoSummary(company.companyMemoList) : ''}
                    </div>
                    <div class="company-actions">
                        <button class="btn btn-outline btn-small view-on-map" data-company-id="${company.id}">
                            地図で確認する
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    generateMemoSummary(memoList) {
        if (!memoList || memoList.length === 0) return '';
        let content = '備考:';
        memoList.forEach(memo => { 
            content = content + `<div class="detail-row">
                <span class="detail-label">${memo.realName}:</span>
                <span class="detail-value">${memo.memo}</span>
            </div>`
        });

        return content
    }
    
    bindCompanyItemEvents() {
        // Company item click for details
        document.querySelectorAll('.company-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('view-on-map')) {
                    const companyId = item.dataset.companyId;
                    this.showCompanyDetails(companyId);
                }
            });
        });
        
        // View on map button click
        document.querySelectorAll('.view-on-map').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const companyId = btn.dataset.companyId;
                this.viewOnMap(companyId);
            });
        });
    }

    // 绑定访问状态选择框事件
    bindVisitStatusEvents() {
        document.querySelectorAll('.visit-status-select').forEach(select => {
            select.addEventListener('change', (e) => {
                e.stopPropagation();
                const companyId = parseInt(select.dataset.companyId);
                const newStatus = select.value;
                this.updateVisitStatus(companyId, newStatus);
            });

            select.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });
    }

    // 更新访问状态
    updateVisitStatus(companyId, newStatus) {
        const company = this.currentResults.find(c => c.id === companyId);
        if (!company) return;

        const defaultMemo = {
            memo: newStatus,
            companyId: companyId
        };
                
        // 发送到服务器
        this.saveVisitStatusToServer(defaultMemo);
    }

    // 保存访问状态到服务器
    saveVisitStatusToServer(memoData) {
        const requestData = {
            id: memoData.companyId,
            memo: memoData.memo
        };

        fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/company/insertOrUpdate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Authorization': 'Bearer ' + localStorage.getItem('authToken')
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(res => {
            console.log('访问状态保存成功:', res);
            if (res.code == 200) {
                // 更新本地数据
                const company = this.currentResults.find(c => c.id === memoData.companyId);
                if (company) {
                    company.memo = memoData.memo;

                    // 更新UI
                    this.sortAndDisplayResults();
                }
            }
        })
        .catch(error => {
            console.error('访问状态保存失败:', error);
            alert('访问状态保存失败，请重试');
        });
    }

    showCompanyDetails(companyId) {
        const company = DataUtils.getCompanyById(companyId);
        if (!company) return;
        
        // Create modal or detailed view
        const modal = this.createCompanyModal(company);
        document.body.appendChild(modal);
        
        // Show modal
        setTimeout(() => modal.classList.add('show'), 10);
    }
    
    createCompanyModal(company) {
        console.log('Creating company modal for company:', company);
        const modal = document.createElement('div');
        modal.className = 'map-overlay';

        // 保存this引用以便在回调函数中使用
        const self = this;
        
        let content = '';
        company.notes = '';
        company.companyMemoList.forEach(memo => {
            content = content + `<div class="detail-row">
                <span class="detail-label">${memo.realName}:</span>
                <span class="detail-value">${memo.memo}</span>
            </div>`
            if (memo.userId == localStorage.getItem('userId')) {
                company.notes = memo.memo;
                company.memoId = memo.id;
            }
        })
        if (content == '') {
            content = `<div class="detail-row">
                備考なし
            </div>`
        }
        modal.innerHTML = `
            <div class="company-panel show">
                <div class="panel-header">
                    <h3>会社詳細</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="panel-content">
                    <div class="company-detail">
                        <h4>${company.name}</h4>
                        <h5>${company.address}</h5>
                        
                        ${company.industry == '' ? '<div></div>' : `<div class="company-industry-tag">${company.industry}</div>`}

                        <div class="detail-row">
                            <span class="detail-label">日本社名:</span>
                            <span class="detail-value">${company.nameJp}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">電話:</span>
                            <span class="detail-value">${company.phoneNumber}</span>
                        </div>
                        <div class="detail-row" >
                            <span class="detail-label">備考:</span>

                            <div class="detail-value" id="notes-section">
                                ${content}
                            </div>
                            
                            <button class="btn btn-outline btn-small edit-notes-btn" style="margin-left: 10px;">編集</button>
                        </div>
                        <div class="detail-row" id="edit-notes-section" style="display: none;">
                            <span class="detail-label">備考編集:</span>
                            <div style="flex: 1;">
                                <textarea id="notes-textarea" class="notes-textarea" rows="3" placeholder="ここに備考を入力してください">${company.notes || ''}</textarea>
                                <div style="margin-top: 10px;">
                                    <button class="btn btn-primary save-notes-btn">保存</button>
                                    <button class="btn btn-secondary cancel-notes-btn" style="margin-left: 10px;">キャンセル</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Bind close event
        modal.querySelector('.close-btn').addEventListener('click', () => {
            modal.remove();
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        // Bind edit notes button event
        const editNotesBtn = modal.querySelector('.edit-notes-btn');
        const editNotesSection = modal.querySelector('#edit-notes-section');
        const notesTextarea = modal.querySelector('#notes-textarea');
        const notesSection = modal.querySelector('#notes-section');
        
        editNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            editNotesBtn.style.display = 'none';
            editNotesSection.style.display = 'flex';
        });
        
        // Bind cancel notes button event
        const cancelNotesBtn = modal.querySelector('.cancel-notes-btn');
        cancelNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            notesTextarea.value = company.notes || '';
            editNotesBtn.style.display = 'inline-block';
            editNotesSection.style.display = 'none';
        });
        
        // Bind save notes button event
        const saveNotesBtn = modal.querySelector('.save-notes-btn');
        saveNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const newNotes = notesTextarea.value;
           
            // Save to server
            const requestData={
                id: company.notes == '' ? '' : company.memoId,
                memo: newNotes,
                companyId: company.id
            }
            fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/companyMemo/insertOrUpdate',{
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Authorization': 'Bearer ' + localStorage.getItem('authToken')
                        },
                        body: JSON.stringify(requestData)   
                    }).then(response => response.json())
            .then(res => {
                console.log(res);
                // Handle the response from the server
                 const data = {
                    id: res.data.id,
                    memo: newNotes,
                    userId: localStorage.getItem('userId'),
                    realName: localStorage.getItem('realName')
                };
                let count = 0
                company.companyMemoList.forEach(memo=> {
                    if (memo.id == res.data.id) {
                        memo.memo = newNotes;
                        count++
                    }
                })
                if (count == 0) {
                    company.companyMemoList.push(data);
                }
                notesSection.innerHTML = ''
                content = '';
                company.notes = '';
                company.companyMemoList.forEach(memo => {
                    content = content + `<div class="detail-row">
                        <span class="detail-label">${memo.realName}:</span>
                        <span class="detail-value">${memo.memo}</span>
                    </div>`
                    if (memo.userId == localStorage.getItem('userId')) {
                        company.notes = memo.memo;
                        company.memoId = memo.id;
                    }
                })
                if (content == '') {
                    content = `<div class="detail-row">
                        備考なし
                    </div>`
                }
                notesSection.innerHTML = content;
                // Update the displayed notes
                editNotesBtn.style.display = 'inline-block';
                editNotesSection.style.display = 'none';

                // 实时更新当前页面的公司列表
                self.updateCompanyListDisplay();

            })
            .catch(error => {
                console.error('Error saving company notes:', error);
            });
        });
        
        return modal;
    }

    saveCompanyNotes(company, data) {
        // 更新公司的companyMemoList
        if (!company.companyMemoList) {
            company.companyMemoList = [];
        }

        // 添加新的备注到列表
        const newMemo = {
            id: Date.now(), // 临时ID
            userId: localStorage.getItem('userId') || 'user',
            memo: data.memo,
            createTime: new Date().toISOString()
        };

        company.companyMemoList.push(newMemo);

        // 保存到localStorage作为备份
        let companyNotes = JSON.parse(localStorage.getItem('companyNotes') || '{}');
        companyNotes[company.id] = data.memo;
        localStorage.setItem('companyNotes', JSON.stringify(companyNotes));

        // 实时更新当前页面的公司列表
        this.updateCompanyListDisplay();

        // 发送到服务器
        const requestData = {
            id: company.notes == '' ? '' : company.memoId,
            memo: data.memo,
            companyId: company.id
        };

        fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/companyMemo/insertOrUpdate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Authorization': 'Bearer ' + localStorage.getItem('authToken')
            },
            body: JSON.stringify(requestData)
        }).then(response => response.json())
        .then(data => {
            console.log('备注保存成功:', data);
        })
        .catch(error => {
            console.error('备注保存失败:', error);
        });
    }

    // 更新公司列表显示
    updateCompanyListDisplay() {
        // 如果有搜索结果，重新排序和显示
        if (this.currentResults && this.currentResults.length > 0) {
            // 重新排序结果
            const sortedResults = DataUtils.sortCompanies(this.currentResults, this.currentSort);

            // 更新结果计数
            this.updateResultsCount(sortedResults.length);

            // 显示当前页面的结果
            this.displayResults(sortedResults);

            // 更新分页
            this.updatePagination(sortedResults.length);
        } else {
            // 如果没有搜索结果，显示初始状态
            this.displayInitialState();
        }
    }
    
    loadCompanyNotes() {
        // Load notes from localStorage
        let companyNotes = JSON.parse(localStorage.getItem('companyNotes') || '{}');
        
        // Apply notes to companies
        DataUtils.getAllCompanies().then(companies => {
            companies.forEach(company => {
                if (companyNotes[company.id]) {
                    company.notes = companyNotes[company.id];
                }
            });
        });
    }
    
    viewOnMap(companyId) {
        // Store company ID in session storage for map page
        sessionStorage.setItem('selectedCompanyId', companyId);
        
        // Navigate to map page
        window.location.href = 'map.html';
    }
    
    updatePagination(totalResults) {
        const totalPages = Math.ceil(totalResults / this.itemsPerPage);
        
        if (totalPages <= 1) {
            this.paginationContainer.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // Previous button
        if (this.currentPage > 1) {
            html += `<button class="pagination-btn" data-page="${this.currentPage - 1}">前へ</button>`;
        }
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            html += `<button class="pagination-btn" data-page="1">1</button>`;
            if (startPage > 2) {
                html += `<span class="pagination-info">...</span>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            html += `<button class="pagination-btn ${activeClass}" data-page="${i}">${i}</button>`;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<span class="pagination-info">...</span>`;
            }
            html += `<button class="pagination-btn" data-page="${totalPages}">${totalPages}</button>`;
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            html += `<button class="pagination-btn" data-page="${this.currentPage + 1}">次へ</button>`;
        }
        
        // Page info
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, totalResults);
        html += `<span class="pagination-info">${startItem}-${endItem} / ${totalResults}</span>`;
        
        this.paginationContainer.innerHTML = html;
        
        // Bind pagination events
        this.bindPaginationEvents();
    }
    
    bindPaginationEvents() {
        document.querySelectorAll('.pagination-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const page = parseInt(btn.dataset.page);
                if (page && page !== this.currentPage) {
                    this.currentPage = page;
                    this.sortAndDisplayResults();
                    
                    // Scroll to top of results
                    this.scrollTag.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    }
    
    clearSearch() {
        // Clear form fields
        this.companyNameInput.value = '';
        this.industrySelect.value = '';

        // Reset radio buttons to default (first option)
        this.prefectureRadios.forEach(radio => {
            radio.checked = radio.value === '';
        });
        this.cityRadios.forEach(radio => {
            radio.checked = radio.value === '';
        });

        // Hide city groups
        this.city1Group.style.display = 'none';
        this.city2Group.style.display = 'none';
        
        // Reset state
        this.currentResults = [];
        this.currentPage = 1;
        this.currentSort = '';
        this.sortSelect.value = '';
        this.currentFilter = '';
        this.filterSelect.value = '';
        
        // Display initial state
        this.displayInitialState();
        this.paginationContainer.innerHTML = '';
    }
    
    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <h3>検索結果がありません</h3>
                <p>検索条件を変更して再度お試しください</p>
            </div>
        `;
    }
}

// Initialize search functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CompanySearch();
});
