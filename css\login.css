/* 登录页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', <PERSON>l, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* 背景装饰 */
.login-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.circle-4 {
    width: 120px;
    height: 120px;
    bottom: 15%;
    right: 10%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

/* 登录卡片 */
.login-card {
    background: white;
    border-radius: 20px;
    padding: 3rem 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-title {
    font-size: 1.5rem;
    color: #333;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 表单样式 */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 1rem;
    color: #999;
    z-index: 3;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    font-size: 1rem;
    background-color: #fafafa;
    transition: all 0.3s ease;
    outline: none;
}

.input-wrapper input:focus {
    border-color: #87ceeb;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(135, 206, 235, 0.1);
}

.input-wrapper input::placeholder {
    color: #bbb;
    font-size: 0.95rem;
}

/* 登录按钮 */
.login-button {
    background: linear-gradient(135deg, #87ceeb 0%, #5dade2 100%);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(135, 206, 235, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.button-text {
    transition: opacity 0.3s ease;
}

.loading-spinner {
    position: absolute;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息样式 */
.login-footer {
    margin-top: 1.5rem;
    text-align: center;
}

.error-message {
    background-color: #fee;
    color: #c33;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #fcc;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.success-message {
    background-color: #efe;
    color: #363;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #cfc;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-card {
        margin: 1rem;
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }
    
    .login-title {
        font-size: 1.3rem;
    }
    
    .input-wrapper input {
        padding: 0.875rem 0.875rem 0.875rem 2.75rem;
        font-size: 0.95rem;
    }
    
    .input-icon {
        left: 0.875rem;
    }
    
    .login-button {
        padding: 0.875rem 1.5rem;
        font-size: 0.95rem;
    }
    
    .bg-circle {
        display: none;
    }
}

/* 动画效果 */
.login-card {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.input-wrapper {
    animation: fadeIn 0.8s ease-out;
}

.input-wrapper:nth-child(1) {
    animation-delay: 0.2s;
}

.input-wrapper:nth-child(2) {
    animation-delay: 0.4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
