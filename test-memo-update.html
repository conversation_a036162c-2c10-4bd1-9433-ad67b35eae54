<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>备注更新测试</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .test-results {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 备注更新功能测试</h1>
        
        <div class="test-section">
            <h2>测试步骤</h2>
            <ol>
                <li>点击"搜索公司"按钮加载公司数据</li>
                <li>点击任意公司查看详情</li>
                <li>在弹窗中编辑备注并保存</li>
                <li>观察列表是否实时更新</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>测试控制</h2>
            <button class="test-button" onclick="searchCompanies()">搜索公司</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
            <button class="test-button" onclick="checkConsole()">检查控制台</button>
        </div>
        
        <div class="test-section">
            <h2>搜索结果</h2>
            <div id="results-count">0 件の結果</div>
            <div id="results-container"></div>
        </div>
        
        <div class="test-results">
            <h3>测试说明</h3>
            <p>此页面用于测试备注修改后的实时列表更新功能。</p>
            <p>请打开浏览器控制台查看调试信息。</p>
        </div>
    </div>

    <script src="js/data.js"></script>
    <script src="js/search.js"></script>
    <script>
        let companySearch;
        
        function searchCompanies() {
            console.log('Starting company search test...');
            
            // 初始化搜索类
            if (!companySearch) {
                companySearch = new CompanySearch();
            }
            
            // 模拟搜索所有公司
            DataUtils.getAllCompanies().then(companies => {
                console.log('Loaded companies:', companies.length);
                companySearch.currentResults = companies.slice(0, 5); // 只显示前5个
                companySearch.sortAndDisplayResults();
            });
        }
        
        function clearResults() {
            document.getElementById('results-container').innerHTML = '';
            document.getElementById('results-count').textContent = '0 件の結果';
        }
        
        function checkConsole() {
            console.log('=== 当前状态检查 ===');
            console.log('companySearch:', companySearch);
            if (companySearch) {
                console.log('currentResults:', companySearch.currentResults);
                console.log('currentResults length:', companySearch.currentResults?.length);
            }
        }
        
        // 页面加载完成后自动搜索
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Test page loaded');
            setTimeout(searchCompanies, 1000);
        });
    </script>
</body>
</html>
