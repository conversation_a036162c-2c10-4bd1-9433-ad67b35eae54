// 地图页面功能，集成高德地图
class CompanyMap {
    constructor() {
        this.map = null;
        this.markers = [];
        this.clusters = [];
        this.infoWindows = []; // 信息窗口数组
        this.currentZoom = 11; // 降低初始缩放级别以显示聚合
        DataUtils.getAllCompanies()
        .then(companies => {
            console.log('All companies:', companies);
            // 过滤掉没有坐标的公司
            const validCompanies = companies.filter(company => 
                company.coordinates && 
                Array.isArray(company.coordinates) && 
                company.coordinates.length === 2 &&
                company.coordinates[0] !== 0 && 
                company.coordinates[1] !== 0
            );
            
            if (validCompanies.length === 0) {
                console.warn('No valid companies with coordinates found');
                // 使用默认数据或显示提示信息
            }
            
            this.filteredCompanies = validCompanies;
            this.initializeElements();
            this.bindEvents();
            this.initializeMap();
        })
        .catch(error => {
            console.error('Failed to initialize companies data:', error);
            // 即使没有公司数据也初始化地图
            this.filteredCompanies = [];
            this.initializeElements();
            this.bindEvents();
            this.initializeMap();
        });

        
    }
    
    initializeElements() {
        // 地图控制元素
        this.mapSearchInput = document.getElementById('map-search');
        this.mapSearchBtn = document.getElementById('map-search-btn');
        this.mapIndustrySelect = document.getElementById('map-industry');
        this.clearFiltersBtn = document.getElementById('clear-map-filters');
        this.mapResultsCount = document.getElementById('map-results-count');

        // 左侧公司列表元素
        this.companyListCount = document.getElementById('company-list-count');
        this.companyListContent = document.getElementById('company-list-content');

        // 面板元素
        this.companyPanel = document.getElementById('company-panel');
        this.clusterPanel = document.getElementById('cluster-panel');
        this.closePanelBtn = document.getElementById('close-panel');
        this.closeClusterPanelBtn = document.getElementById('close-cluster-panel');
        this.panelContent = document.getElementById('panel-content');
        this.clusterContent = document.getElementById('cluster-content');

        // 当前选中的公司
        this.selectedCompanyId = null;
    }
    
    bindEvents() {
        // 搜索功能
        this.mapSearchBtn.addEventListener('click', () => this.performMapSearch());
        this.mapSearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performMapSearch();
            }
        });
        this.mapSearchInput.addEventListener('input', (e) => {
            this.performMapSearch();
        });
        this.mapSearchInput.addEventListener('change', (e) => {
            this.performMapSearch();
        });
        // 筛选功能
        this.mapIndustrySelect.addEventListener('change', () => this.applyFilters());
        this.clearFiltersBtn.addEventListener('click', () => this.clearFilters());

        // 面板关闭事件
        this.closePanelBtn.addEventListener('click', () => this.closeCompanyPanel());
        this.closeClusterPanelBtn.addEventListener('click', () => this.closeClusterPanel());

        // 点击外部关闭面板
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('map-overlay')) {
                this.closeCompanyPanel();
                this.closeClusterPanel();
            }
        });
    }
    
    initializeMap() {
        // 检查高德地图是否可用
        if (typeof AMap === 'undefined') {
            console.error('高德地图未加载。请检查您的API密钥和网络连接。');
            this.showMapError();
            return;
        }

        try {
            // 初始化地图，中心点设置为杭州
            this.map = new AMap.Map('map-area', {
                zoom: this.currentZoom,
                center: [120.125623, 30.158845], // 杭州坐标
                mapStyle: 'amap://styles/normal',
                features: ['bg', 'road', 'building'],
                viewMode: '2D'
            });

            // 添加地图控件
            // AMap.plugin(['AMap.Scale', 'AMap.ToolBar'], () => {
            //     this.map.addControl(new AMap.Scale());
            //     this.map.addControl(new AMap.ToolBar());
            // });

            // 绑定地图事件
            this.map.on('zoomend', () => {
                this.currentZoom = this.map.getZoom();
                this.updateMarkersAndClusters();
            });

            this.map.on('moveend', () => {
                this.updateMarkersAndClusters();
            });

            // 加载公司备注数据
            this.loadCompanyNotes();

            // 初始显示所有公司标记
            this.updateMarkersAndClusters();
            this.updateCompanyList();

            // 检查是否有从搜索页面选中的公司
            this.checkForSelectedCompany();

        } catch (error) {
            console.error('地图初始化错误:', error);
            this.showMapError();
        }
    }
    
    showMapError() {
        const mapContainer = document.getElementById('map-container');
        mapContainer.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background-color: #f5f5f5;">
                <div style="text-align: center; padding: 2rem;">
                    <h3 style="color: #666; margin-bottom: 1rem;">地図を読み込めませんでした</h3>
                    <p style="color: #999;">AMap APIキーを確認してください</p>
                    <p style="color: #999; font-size: 0.9rem;">デモ用のため、実際のAPIキーが必要です</p>
                </div>
            </div>
        `;
    }
    
    checkForSelectedCompany() {
        // Check if a company was selected from the search page
        console.log('Checking for selected company...');
        const selectedCompanyId = sessionStorage.getItem('selectedCompanyId');
        if (selectedCompanyId) {
            sessionStorage.removeItem('selectedCompanyId');
            console.log('Selected company ID:', selectedCompanyId);
            const company = this.filteredCompanies.find(c => c.id === parseInt(selectedCompanyId));
            console.log('Selected company:', company);
            if (company && this.map) {
                // 设置选中状态
                this.selectCompany(parseInt(selectedCompanyId));

                // Center map on company location
                this.map.setCenter(company.coordinates);
                this.map.setZoom(20);

                // 更新标记状态
                this.updateMarkerStates();

                // Show company details after a short delay
                setTimeout(() => {
                    this.showCompanyDetails(company);
                }, 1000);
            }
        }
    }
    
    performMapSearch() {
        const searchTerm = this.mapSearchInput.value.trim();
        console.log('Searching for:', searchTerm);
        if (!searchTerm) {
            // 如果没有搜索关键字，显示所有公司
            // this.filteredCompanies = DataUtils.getAllCompanies();
            DataUtils.getAllCompanies().then(companies => {
            console.log('All companies:', companies)
                companies = companies.filter(company => company.geographicalCoordinates != ''); // Filter out companies without coordinates
                    
                this.filteredCompanies = companies
                console.log('this.filteredCompanies:', this.filteredCompanies);
                // 更新显示
                this.updateMarkersAndClusters();
                this.updateResultsCount();
                this.updateCompanyList();
                // this.applyIndustryFilter();
            })
        } else {
            // 根据搜索条件筛选公司
            const criteria = { name: searchTerm };
            this.filteredCompanies = DataUtils.searchCompanies(criteria);
            console.log('this.filteredCompanies:', this.filteredCompanies);
            // 更新显示
            this.updateMarkersAndClusters();
            this.updateResultsCount();
            this.updateCompanyList();
            // 应用行业筛选
            // this.applyIndustryFilter();
        }
        
    }
    
    applyFilters() {
        // 从所有公司开始
        // this.filteredCompanies = DataUtils.getAllCompanies();
        DataUtils.getAllCompanies().then(companies => {
            console.log('All companies:', companies)
            companies = companies.filter(company => company.geographicalCoordinates != ''); // Filter out companies without coordinates
                            
            this.filteredCompanies = companies
            // 如果存在搜索条件则应用搜索筛选
            const searchTerm = this.mapSearchInput.value.trim();
            if (searchTerm) {
                const criteria = { name: searchTerm };
                this.filteredCompanies = DataUtils.searchCompanies(criteria);
            }

            // 应用行业筛选
            // this.applyIndustryFilter();

            // 更新显示
            this.updateMarkersAndClusters();
            this.updateResultsCount();
            this.updateCompanyList();
        })
        
    }
    
    applyIndustryFilter() {
        const selectedIndustry = this.mapIndustrySelect.value;
        if (selectedIndustry) {
            this.filteredCompanies = this.filteredCompanies.filter(
                company => company.industry === selectedIndustry
            );
        }
    }
    
    clearFilters() {
        this.mapSearchInput.value = '';
        this.mapIndustrySelect.value = '';
        // this.filteredCompanies = DataUtils.getAllCompanies();
        DataUtils.getAllCompanies().then(companies => {
            console.log('All companies:', companies)
            companies = companies.filter(company => company.geographicalCoordinates != ''); // Filter out companies without coordinates
                            
            this.filteredCompanies = companies
            
            this.updateMarkersAndClusters();
            this.updateResultsCount();
            this.updateCompanyList();
        })
    }
    
    updateResultsCount() {
        const count = this.filteredCompanies.length;
        const searchTerm = this.mapSearchInput.value.trim();
        const industryFilter = this.mapIndustrySelect.value;

        if (count === 0) {
            this.mapResultsCount.textContent = '条件に一致する会社が見つかりませんでした';
        } else if (!searchTerm && !industryFilter) {
            this.mapResultsCount.textContent = `すべての会社を地図上に表示中 (${count}社)`;
        } else {
            this.mapResultsCount.textContent = `${count}社を地図上に表示中`;
        }
    }

    updateCompanyList() {
        const count = this.filteredCompanies.length;
        this.companyListCount.textContent = `${count}社`;

        if (count === 0) {
            this.companyListContent.innerHTML = `
                <div style="padding: 2rem; text-align: center; color: #666;">
                    <p>表示する会社がありません</p>
                </div>
            `;
            return;
        }

        // 按区域排序公司
        // const sortedCompanies = [...this.filteredCompanies].sort((a, b) => {
        //     if (a.district !== b.district) {
        //         return a.district.localeCompare(b.district);
        //     }
        //     return a.name.localeCompare(b.name);
        // });

        const html = this.filteredCompanies.map(company => {
            const isVisited = this.isCompanyVisited(company);
            return `
                <div class="company-list-item ${isVisited ? 'visited' : ''}" data-company-id="${company.id}" id="company-${company.id}">
                    <div class="company-item-header">
                        <div class="company-item-name">${company.name}</div>
                        <select class="visit-status-select" data-company-id="${company.id}">
                            <option value="unvisited" ${!isVisited ? 'selected' : ''}>未訪問</option>
                            <option value="visited" ${isVisited ? 'selected' : ''}>訪問済み</option>
                        </select>
                    </div>
                    <div class="company-item-name-jp">${company.nameJp}</div>
                    <div class="company-item-address">${company.district} - ${company.address}</div>
                    <div style="display: flex;justify-content: space-between;">
                        ${company.industry == '' ? '<div></div>' : `<div class="company-item-industry">${company.industry}</div>`}
                        <div></div>
                    </div>
                    ${isVisited ? this.generateMemoSummary(company.companyMemoList) : ''}
                </div>
            `;
        }).join('');

        this.companyListContent.innerHTML = html;

        // 绑定点击事件
        this.bindCompanyListEvents();
    }

    // 更新公司列表显示（用于实时更新）
    updateCompanyListDisplay() {
        // 重新生成公司列表HTML
        const count = this.filteredCompanies.length;
        this.companyListCount.textContent = `${count}社`;

        if (count === 0) {
            this.companyListContent.innerHTML = `
                <div style="padding: 2rem; text-align: center; color: #666;">
                    <p>表示する会社がありません</p>
                </div>
            `;
            return;
        }

        // 按区域排序公司
        // const sortedCompanies = [...this.filteredCompanies].sort((a, b) => {
        //     if (a.district !== b.district) {
        //         return a.district.localeCompare(b.district);
        //     }
        //     return a.name.localeCompare(b.name);
        // });

        const html = this.filteredCompanies.map(company => {
            const isVisited = this.isCompanyVisited(company);
            return `
                <div class="company-list-item ${isVisited ? 'visited' : ''}" data-company-id="${company.id}" id="company-${company.id}">
                    <div class="company-item-header">
                        <div class="company-item-name">${company.name}</div>
                        <select class="visit-status-select" data-company-id="${company.id}">
                            <option value="unvisited" ${!isVisited ? 'selected' : ''}>未訪問</option>
                            <option value="visited" ${isVisited ? 'selected' : ''}>訪問済み</option>
                        </select>
                    </div>
                    <div class="company-item-name-jp">${company.nameJp}</div>
                    <div class="company-item-address">${company.district} - ${company.address}</div>
                    <div style="display: flex;justify-content: space-between;">
                        ${company.industry == '' ? '<div></div>' : `<div class="company-item-industry">${company.industry}</div>`}
                        <div></div>
                    </div>
                    ${isVisited ? this.generateMemoSummary(company.companyMemoList) : ''}
                </div>
            `;
        }).join('');

        this.companyListContent.innerHTML = html;

        // 重新绑定事件
        this.bindCompanyListEvents();

        // 保持当前选中状态
        if (this.selectedCompanyId) {
            const selectedItem = document.getElementById(`company-${this.selectedCompanyId}`);
            if (selectedItem) {
                selectedItem.classList.add('active');
            }
        }
    }

    bindCompanyListEvents() {
        document.querySelectorAll('.company-list-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('visit-status-select')) {
                    const companyId = parseInt(item.dataset.companyId);
                    this.selectCompany(companyId);
                }
            });
        });

        // 绑定访问状态选择框事件
        document.querySelectorAll('.visit-status-select').forEach(select => {
            select.addEventListener('change', (e) => {
                e.stopPropagation();
                const companyId = parseInt(select.dataset.companyId);
                const newStatus = select.value;
                this.updateVisitStatus(companyId, newStatus);
            });

            select.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        });
    }

    selectCompany(companyId) {
        // 更新选中状态
        this.selectedCompanyId = companyId;

        // 更新列表中的选中状态
        document.querySelectorAll('.company-list-item').forEach(item => {
            item.classList.remove('active');
            if (parseInt(item.dataset.companyId) === companyId) {
                item.classList.add('active');
            }
        });
        // const container = document.getElementById('company-list-content');
        // const target = document.getElementById('company-' + companyId);
        // // 获取目标子元素在父div中的相对位置
        // const targetElementTop = target.offsetTop;
        // console.log(targetElementTop);
        // // 将父div的滚动条滚动到目标子元素的位置
        // // 使用scrollTo实现平滑滚动
        // container.scrollTo({
        //     top: targetElementTop,
        //     behavior: 'smooth' // 平滑滚动
        // });
        const target = document.getElementById('company-' + companyId);
        target.scrollIntoView({ 
            behavior: 'smooth',  // 平滑滚动
            block: 'start',      // 对齐方式
            inline: 'nearest'    // 行内对齐方式
        });
        // 获取公司信息
        const company = DataUtils.getCompanyById(companyId);
        if (!company) return;

        // 移动地图到公司位置
        this.map.setCenter(company.coordinates);
        this.map.setZoom(20);

        // 更新标记状态
        this.updateMarkerStates();
    }

    updateMarkerStates() {
        // 更新所有标记的状态
        this.markers.forEach(marker => {
            const extData = marker.getExtData();

            // 处理主标记
            if (typeof extData === 'number') {
                const companyId = extData;
                const company = DataUtils.getCompanyById(companyId);
                const isVisited = company ? this.isCompanyVisited(company) : false;

                let markerClass = 'custom-marker';
                if (isVisited) {
                    markerClass += ' visited';
                } else {
                    markerClass += ' unvisited';
                }

                if (companyId === this.selectedCompanyId) {
                    markerClass += ' active';
                }

                marker.setContent(`<div class="${markerClass}"></div>`);
            }
            // 标签标记不需要改变内容，只需要确保位置正确
        });
    }
    
    updateMarkersAndClusters() {
        if (!this.map) return;

        // 清除现有标记和聚合
        this.clearMarkersAndClusters();

        // 根据缩放级别决定显示聚合还是单个标记
        if (this.currentZoom < 14) {
            this.showClusters();
        } else if ( this.currentZoom >= 14 && this.currentZoom < 16) {
            this.showIndividualMarkers();
        } else {
            this.showIndividualMarkers();

        }
    }
    
    clearMarkersAndClusters() {
        // 移除现有标记
        this.markers.forEach(marker => {
            this.map.remove(marker);
        });
        this.markers = [];

        // 移除现有聚合
        this.clusters.forEach(cluster => {
            this.map.remove(cluster);
        });
        this.clusters = [];

        // 关闭所有信息窗口
        this.infoWindows.forEach(infoWindow => {
            infoWindow.close();
        });
        this.infoWindows = [];
    }
    
    showClusters() {
        // 按区域分组公司
        const districts = {};
        console.log(this.filteredCompanies);
        this.filteredCompanies.forEach(company => {
            const district = company.district;
            if (!districts[district]) {
                districts[district] = [];
            }
            districts[district].push(company);
        });

        // 创建聚合标记
        Object.entries(districts).forEach(([district, companies]) => {
            if (companies.length === 0) return;

            // 计算该区域内公司的中心点
            const centerLat = companies.reduce((sum, c) => sum + c.coordinates[1], 0) / companies.length;
            const centerLng = companies.reduce((sum, c) => sum + c.coordinates[0], 0) / companies.length;

            // 创建聚合标记
            const clusterMarker = this.createClusterMarker([centerLng, centerLat], companies, district);
            this.map.add(clusterMarker);
            this.clusters.push(clusterMarker);
        });
    }
    
    createClusterMarker(coordinates, companies, district) {
        const count = companies.length;
        let size = 'small';
        if (count > 10) size = 'large';
        else if (count > 5) size = 'medium';

        const marker = new AMap.Marker({
            position: coordinates,
            content: `<div class="cluster-marker large" style="display: flex; flex-direction: column; align-items: center;justify-content: flex-start;">
                        <div style="font-size: 10px;">${district}</div>
                        ${count}
                    </div>`,
            offset: new AMap.Pixel(-25, -25)
        });

        // 添加点击事件
        marker.on('click', () => {
            this.showClusterDetails(district, companies);
        });

        return marker;
    }
    
    showIndividualMarkers() {
        this.filteredCompanies.forEach(company => {
            const marker = this.createCompanyMarker(company);
            this.map.add(marker);
            this.markers.push(marker);
        });
    }
    
    createCompanyMarker(company) {
        // 判断公司是否被拜访过
        const isVisited = this.isCompanyVisited(company);

        // 创建标记点（根据拜访状态设置不同样式）
        const markerContent = `<div class="custom-marker ${isVisited ? 'visited' : 'unvisited'}"></div>`;

        const marker = new AMap.Marker({
            position: company.coordinates,
            content: markerContent,
            anchor: 'center', // 设置锚点为中心，避免缩放时偏移
            extData: company.id // 存储公司ID
        });

        // 创建信息标签（独立的标记，紧贴公司标记）
        const labelContent = `
            <div class="marker-label">
                <div class="marker-label-name">${company.name}</div>
                ${this.currentZoom >= 16 ? `<div class="marker-label-address">${company.address}</div>` : '<div></div>'}
                ${company.industry == '' ? '<div></div>' : `<div class="marker-label-industry">${company.industry}</div>`}
                ${this.currentZoom >= 16 ? '<div></div>' : `<div class="marker-label-address">${company.district}</div>`}
                
            </div>
        `;

        const labelMarker = new AMap.Marker({
            position: company.coordinates,
            content: labelContent,
            anchor: 'bottom-center', // 标签底部中心对齐标记点
            offset: new AMap.Pixel(-10, 0), // 向上偏移，避免重叠
            extData: `label_${company.id}`,
            zIndex: 100 // 确保标签在标记点之上
        });

        // 添加点击事件到主标记
        marker.on('click', () => {
            this.selectCompany(company.id);
        });

        // 添加双击事件显示详细信息
        marker.on('dblclick', () => {
            this.showCompanyDetails(company);
        });

        // 标签也可以点击
        labelMarker.on('click', () => {
            this.selectCompany(company.id);
        });

        // 标签也可以点击
        labelMarker.on('dblclick', () => {
            this.showCompanyDetails(company);
        });
        // 将标签标记也添加到地图
        this.map.add(labelMarker);
        this.markers.push(labelMarker); // 同时管理标签标记

        return marker;
    }
    
    showCompanyDetails(company) {
        this.closeClusterPanel();

        // // 加载公司备注
        // this.loadCompanyNotes();

        let content = '';
        company.notes = '';
        company.companyMemoList.forEach(memo => {
            content = content + `<div class="detail-row">
                <span class="detail-label">${memo.realName}:</span>
                <span class="detail-value">${memo.memo}</span>
            </div>`
            if (memo.userId == localStorage.getItem('userId')) {
                company.notes = memo.memo;
                company.memoId = memo.id;
            }
        })
        if (content == '') {
            content = `<div class="detail-row">
                備考なし
            </div>`
        }
        // Populate panel content with notes editing functionality
        this.panelContent.innerHTML = `
            <div class="company-detail">
                <h4>${company.name}</h4>
                <h5>${company.address}</h5>

                ${company.industry == '' ? '<div></div>' : `<div class="company-industry-tag">${company.industry}</div>`}

                <div class="detail-row">
                    <span class="detail-label">日本社名:</span>
                    <span class="detail-value">${company.nameJp}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">電話:</span>
                    <span class="detail-value">${company.phoneNumber}</span>
                </div>
                <div class="detail-row" >
                    <span class="detail-label">備考:</span>

                    <div class="detail-value" id="notes-section">
                        ${content}
                    </div>
                            
                    <button class="btn btn-outline btn-small edit-notes-btn" style="margin-left: 10px;">編集</button>
                </div>
                <div class="detail-row" id="edit-notes-section" style="display: none;">
                    <span class="detail-label">備考編集:</span>
                    <div style="flex: 1;">
                        <textarea id="notes-textarea" class="notes-textarea" rows="3" placeholder="ここに備考を入力してください">${company.notes || ''}</textarea>
                        <div style="margin-top: 10px;">
                            <button class="btn btn-primary save-notes-btn">保存</button>
                            <button class="btn btn-secondary cancel-notes-btn" style="margin-left: 10px;">キャンセル</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Bind edit notes functionality
        this.bindNotesEditingEvents(company);

        // Show panel
        this.companyPanel.classList.add('show');
        document.querySelector('.map-overlay').classList.add('show');
    }
    
    bindNotesEditingEvents(company) {
        const editNotesBtn = this.panelContent.querySelector('.edit-notes-btn');
        const editNotesSection = this.panelContent.querySelector('#edit-notes-section');
        const notesTextarea = this.panelContent.querySelector('#notes-textarea');
        const notesSection = this.panelContent.querySelector('#notes-section');

        // Edit notes button event
        editNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            editNotesBtn.style.display = 'none';
            editNotesSection.style.display = 'flex';
        });

        // Cancel notes button event
        const cancelNotesBtn = this.panelContent.querySelector('.cancel-notes-btn');
        cancelNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            notesTextarea.value = company.notes || '';
            editNotesBtn.style.display = 'inline-block';
            editNotesSection.style.display = 'none';
        });

        // Save notes button event
        const saveNotesBtn = this.panelContent.querySelector('.save-notes-btn');
        saveNotesBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const newNotes = notesTextarea.value;
            // Save to server
            const requestData={
                id: company.notes == '' ? '' : company.memoId,
                memo: newNotes,
                companyId: company.id
            }
            fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/companyMemo/insertOrUpdate',{
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Authorization': 'Bearer ' + localStorage.getItem('authToken')
                        },
                        body: JSON.stringify(requestData)   
                    }).then(response => response.json())
            .then(res => {
                console.log(res);
                // Handle the response from the server
                 const data = {
                    id: res.data.id,
                    memo: newNotes,
                    userId: localStorage.getItem('userId'),
                    realName: localStorage.getItem('realName')
                };
                let count = 0
                company.companyMemoList.forEach(memo=> {
                    if (memo.id == res.data.id) {
                        memo.memo = newNotes;
                        count++
                    }
                })
                if (count == 0) {
                    company.companyMemoList.push(data);
                }
                notesSection.innerHTML = ''
                let content = '';
                company.notes = '';
                company.companyMemoList.forEach(memo => {
                    content = content + `<div class="detail-row">
                        <span class="detail-label">${memo.realName}:</span>
                        <span class="detail-value">${memo.memo}</span>
                    </div>`
                    if (memo.userId == localStorage.getItem('userId')) {
                        company.notes = memo.memo;
                        company.memoId = memo.id;
                    }
                })
                if (content == '') {
                    content = `<div class="detail-row">
                        備考なし
                    </div>`
                }
                notesSection.innerHTML = content;
                // Update the displayed notes
                editNotesBtn.style.display = 'inline-block';
                editNotesSection.style.display = 'none';

                // 实时更新所有相关UI组件
                this.updateCompanyListDisplay();
                this.updateMarkersAndClusters();
                this.updateMarkerStates();

            })
            .catch(error => {
                console.error('Error saving company notes:', error);
            });
        });
    }

    saveCompanyNotes(company, data) {
        // Save to localStorage
        const requestData={
            id: company.notes == '' ? '' : company.memoId,
            memo: data.memo,
            companyId: company.id
        }
       fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/companyMemo/insertOrUpdate',{
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Authorization': 'Bearer ' + localStorage.getItem('authToken')
                    },
                    body: JSON.stringify(requestData)   
                }).then(response => response.json())
        .then(data => {
            // Handle the response from the server
        })
        .catch(error => {
            console.error('Error saving company notes:', error);
        });
    }

    loadCompanyNotes() {
        // Load notes from localStorage
        let companyNotes = JSON.parse(localStorage.getItem('companyNotes') || '{}');

        // Apply notes to all companies
        DataUtils.getAllCompanies().then(companies => {
            companies.forEach(company => {
                if (companyNotes[company.id]) {
                    company.notes = companyNotes[company.id];
                }
            });
        });

        // Also apply to filtered companies
        this.filteredCompanies.forEach(company => {
            if (companyNotes[company.id]) {
                company.notes = companyNotes[company.id];
            }
        });
    }

    // 判断公司是否被拜访过（根据company.memo的值是否为visited）
    isCompanyVisited(company) {
        return company.memo == 'visited';
    }

    // 更新访问状态
    updateVisitStatus(companyId, newStatus) {
        const company = this.filteredCompanies.find(c => c.id === companyId);
        if (!company) return;
        const defaultMemo = {
            memo: newStatus,
            companyId: companyId
        };
                
        // 发送到服务器
        this.saveVisitStatusToServer(defaultMemo);
    }

    // 保存访问状态到服务器
    saveVisitStatusToServer(memoData) {
        const requestData = {
            id: memoData.companyId,
            memo: memoData.memo
        };

        fetch('https://metaverse.fujisoft-china.com:7796/companymap-api/company/insertOrUpdate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Authorization': 'Bearer ' + localStorage.getItem('authToken')
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(res => {
            console.log('访问状态保存成功:', res);
            if (res.code == 200) {
            }
        })
        .catch(error => {
            console.error('访问状态保存失败:', error);
            alert('访问状态保存失败，请重试');
        });
    }

    generateMemoSummary(memoList) {
        if (!memoList || memoList.length === 0) return '';
        let content = '備考:';
        memoList.forEach(memo => { 
            content = content + `<div class="detail-row">
                <span class="detail-label">${memo.userId}:</span>
                <span class="detail-value">${memo.memo}</span>
            </div>`
        });

        return content
    }

    showClusterDetails(district, companies) {
        this.closeCompanyPanel();
        
        // Populate cluster panel content
        this.clusterContent.innerHTML = `
            <div class="cluster-summary">
                <div class="cluster-count">${companies.length}</div>
                <div class="cluster-area">${district}の会社</div>
            </div>
            <div class="cluster-companies">
                ${companies.map(company => `
                    <div class="cluster-company-item" data-company-id="${company.id}">
                        <div class="cluster-company-name">${company.name}</div>
                        <div class="cluster-company-address">${company.address}</div>
                    </div>
                `).join('')}
            </div>
        `;
        
        // Bind click events for company items
        this.clusterContent.querySelectorAll('.cluster-company-item').forEach(item => {
            item.addEventListener('click', () => {
                const companyId = item.dataset.companyId;
                const company = DataUtils.getCompanyById(companyId);
                if (company) {
                    this.closeClusterPanel();
                    this.map.setCenter(company.coordinates);
                    this.map.setZoom(15);
                    this.selectCompany(companyId)
                    setTimeout(() => this.showCompanyDetails(company), 500);
                }
            });
        });
        
        // Show cluster panel
        this.clusterPanel.classList.add('show');
        document.querySelector('.map-overlay').classList.add('show');
    }
    
    closeCompanyPanel() {
        this.companyPanel.classList.remove('show');
        document.querySelector('.map-overlay').classList.remove('show');
    }
    
    closeClusterPanel() {
        this.clusterPanel.classList.remove('show');
        document.querySelector('.map-overlay').classList.remove('show');
    }
}

// Initialize map functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CompanyMap();
});
