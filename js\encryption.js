// RSA和AES混合加密工具类
class EncryptionUtils {
    constructor() {
        // RSA公钥 (实际项目中应该从服务器获取)
        this.rsaPublicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmWPDFsI9N+4Hsmo2
kwtPENGaJ+9Kiq4Nqscdac0LHfHLTEhPuPnnfYZ+g050Ag4IoS+C6U3MQM1P
gi4IuhL4Ef2awrMJxf4HwuVxtIZy+oiT8SRQU84/jgcpi9Omoi2wKcRc5Bpr
fp4wI2nzb9bS397KsPA0b/HSix+xlCkBCqTyL3HjFOBNSxsU3o0F0DyesomK
G5B6J9Csj8ALWLs/JUsZNfO1tMyHN2invduNPwvn7dETAGCeQ1t6Of986twv
EE8jDxEPQ35mZVF2cDkcvSSb8aJLzlyZS6zzmYF1UsfT+Z5j3lYUIIbVfacR
3V/FYL4qZx+0eYY2VrgWhoEZqQIDAQAB
-----END PUBLIC KEY-----`;
        
        this.jsEncrypt = new JSEncrypt();
        this.jsEncrypt.setPublicKey(this.rsaPublicKey);
    }
    
    /**
     * 生成随机AES密钥
     * @returns {string} 32字符的随机密钥
     */
    generateAESKey() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 32; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    /**
     * AES加密数据
     * @param {string} data - 要加密的数据
     * @param {string} key - AES密钥
     * @returns {string} 加密后的数据
     */
    aesEncrypt(data, key) {
        try {
            const keyWords = CryptoJS.enc.Utf8.parse(key);
            const encrypted = CryptoJS.AES.encrypt(data, keyWords, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7
            });
            return encrypted.toString();
        } catch (error) {
            console.error('AES加密失败:', error);
            throw new Error('数据加密失败');
        }
    }
    
    /**
     * RSA加密AES密钥
     * @param {string} aesKey - AES密钥
     * @returns {string} RSA加密后的AES密钥
     */
    rsaEncrypt(aesKey) {
        try {
            const encrypted = this.jsEncrypt.encrypt(aesKey);
            if (!encrypted) {
                throw new Error('RSA加密失败');
            }
            return encrypted;
        } catch (error) {
            console.error('RSA加密失败:', error);
            throw new Error('密钥加密失败');
        }
    }
    
    /**
     * 混合加密登录数据
     * @param {Object} loginData - 登录数据 {username, password}
     * @returns {Object} 加密后的数据包
     */
    encryptLoginData(loginData) {
        try {
            // 1. 生成随机AES密钥
            // const aesKey = this.generateAESKey();
            // console.log('生成AES密钥:', aesKey);
            
            // 2. 将登录数据转换为JSON字符串
            const dataString = JSON.stringify(loginData);
            console.log('原始数据:', dataString);
            
            // 3. 使用AES加密登录数据
            // const encryptedData = this.aesEncrypt(dataString, aesKey);
            // console.log('AES加密后的数据:', encryptedData);
            
            // 4. 使用RSA加密AES密钥
            const encryptedKey = this.rsaEncrypt(dataString);
            console.log('RSA加密后的密钥:', encryptedKey);
            
            // 5. 组合传输数据
            const payload = {
                parameter: encryptedKey
            };
            
            console.log('最终传输数据:', payload);
            return payload;
            
        } catch (error) {
            console.error('加密过程失败:', error);
            throw error;
        }
    }
    
    /**
     * 验证加密数据的完整性
     * @param {Object} payload - 加密数据包
     * @returns {boolean} 是否有效
     */
    validatePayload(payload) {
        return payload && 
               payload.parameter
    }
}

// 创建全局加密工具实例
window.EncryptionUtils = new EncryptionUtils();
