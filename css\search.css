/* Search Page Specific Styles */
.mainSearch {
    padding: 2rem 0;
}
.search-section {
    /* background-color: white; */
    border-radius: 8px;
    /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
    padding: 2rem 2rem 1rem 2rem;
    margin-top: 2rem;
}

.search-header {
    text-align: center;
    margin-bottom: 2rem;
}

.search-header h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.search-header p {
    color: #666;
    font-size: 1.1rem;
}

.search-form {
    max-width: 800px;
    /* margin: 0 auto; */
}

.search-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.search-field {
    display: flex;
    flex-direction: column;
}

.search-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

/* Results Section */
.results-section {
    /* background-color: white; */
    border-radius: 8px;
    /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
    padding: 2rem;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.results-header h3 {
    color: #2c3e50;
    font-size: 1.3rem;
}

.results-controls select {
    width: auto;
    min-width: 150px;
}

.results-container {
    min-height: 400px;
}

.company-item {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.company-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: #3498db;
}

.company-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.company-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.company-name-jp {
    font-size: 0.9rem;
    color: #666;
}

.company-industry {
    background-color: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.company-details {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    align-items: flex-start;
}

.company-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.company-address {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.company-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    white-space: nowrap;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background-color: white;
    color: #666;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background-color: #f8f9fa;
    border-color: #3498db;
    color: #3498db;
}

.pagination-btn.active {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    margin: 0 1rem;
    color: #666;
    font-size: 0.9rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #999;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 3rem 1rem;
}

.loading-state .loading {
    width: 40px;
    height: 40px;
    border-width: 4px;
    margin-bottom: 1rem;
}

/* Company Details Panel (Map Modal Style) */
.map-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.company-panel,
.cluster-panel {
    position: relative;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 1001;
    display: none;
    overflow: hidden;
}

.company-panel.show,
.cluster-panel.show {
    display: block;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.panel-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background-color: #e9ecef;
    color: #333;
}

.panel-content {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

.company-detail {
    margin-bottom: 1.5rem;
}

.company-detail:last-child {
    margin-bottom: 0;
}

.company-detail h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.company-detail-jp {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.company-industry-tag {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
}

.detail-row {
    display: flex;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.detail-label {
    font-weight: 500;
    color: #555;
    min-width: 80px;
    margin-right: 1rem;
}

.detail-value {
    color: #333;
    flex: 1;
}

.detail-value a {
    color: #3498db;
    text-decoration: none;
}

.detail-value a:hover {
    text-decoration: underline;
}

.notes-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    resize: vertical;
}

.notes-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .search-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .results-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .company-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
    
    .company-details {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .company-actions {
        flex-direction: row;
        justify-content: flex-start;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .pagination-info {
        width: 100%;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .company-panel {
        width: 95%;
        max-height: 90vh;
    }
    
    .panel-content {
        max-height: calc(90vh - 80px);
        padding: 1rem;
    }
    
    .panel-header {
        padding: 0.75rem 1rem;
    }
    
    .detail-row {
        flex-direction: column;
        margin-bottom: 0.75rem;
    }
    
    .detail-label {
        min-width: auto;
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 480px) {
    .search-section,
    .results-section {
        padding: 1rem;
    }
    
    .search-header h2 {
        font-size: 1.5rem;
    }
    
    .company-item {
        padding: 1rem;
    }
    
    .company-name {
        font-size: 1.1rem;
    }
}