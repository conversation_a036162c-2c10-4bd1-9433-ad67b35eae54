/* Map Page Specific Styles */

.map-controls {
    /* background-color: white; */
    padding: 1rem 2rem;
    /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
    gap: 1rem;
    position: fixed;
    z-index: 100;
    width: 100%;
    left: 0;
    top: 4rem;
    height: 4rem;
    box-sizing: border-box;
}

.search-mini {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.search-mini input {
    width: 300px;
    margin: 0;
    border-radius: 20px;
}

.map-filters {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.map-filters select {
    width: auto;
    min-width: 150px;
    margin: 0;
}

.map-info {
    color: #666;
    font-size: 0.9rem;
}

.map-container {
    height: calc(100vh - 4rem);
    min-height: 500px;
    position: relative;
    background-color: #f0f0f0;
    display: flex;
}

/* 左侧公司列表面板 */
.company-list-panel {
    width: 300px;
    background-color: white;
    border-right: 1px solid #ddd;
    overflow-y: auto;
    z-index: 10;
    position: fixed;
    left: 2rem;
    top: 8rem;
    height: calc(100vh - 10rem);
    box-sizing: border-box;
    border-radius: 20px;
}

.company-list-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 11;
}

.company-list-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.company-list-count {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

.company-list-content {
    padding: 0;
}

.company-list-item {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.company-list-item:hover {
    background-color: #f8f9fa;
}

.company-list-item.active {
    background-color: #e3f2fd;
    /* border-left: 4px solid #3498db; */
}

.company-item-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    line-height: 1.3;
    width: 200px;
}

.company-item-name-jp {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.company-item-industry {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    margin-bottom: 0.25rem;
    width: 120px;
    white-space: wrap;
}

.company-item-map {
    display: inline-block;
    background-color: #FFFFFF;
    color: #3498db;
    border: 1px solid #3498db;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    margin-bottom: 0.25rem;
}

.company-item-address {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.3;
    margin-bottom: 0.25rem;
}

/* 公司列表拜访状态样式 */
.company-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.visit-status {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-weight: 500;
}

.visit-status:not(.unvisited) {
    background-color: #d4edda;
    color: #155724;
}

.visit-status.unvisited {
    background-color: #f8d7da;
    color: #721c24;
}

/* 访问状态选择框样式 */
.visit-status-select {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    background-color: white;
    color: #333;
    font-weight: 500;
    cursor: pointer;
    width: 4rem;
    transition: all 0.2s ease;
}

.visit-status-select:hover {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.visit-status-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.visit-status-select option[value="visited"] {
    background-color: #d4edda;
    color: #155724;
}

.visit-status-select option[value="unvisited"] {
    background-color: #f8d7da;
    color: #721c24;
}

.company-list-item.visited {
    /* border-left: 4px solid #27ae60; */
    background-color: #f8fff9;
}

.company-list-item.visited:hover {
    background-color: #e8f5e8;
}

.company-item-notes {
    font-size: 0.75rem;
    color: #666;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 0.5rem;
    /* border-left: 3px solid #27ae60; */
}

/* 地图页面备注摘要样式 */
.memo-count {
    font-size: 0.75rem;
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.25rem;
}

.memo-latest {
    font-size: 0.7rem;
    color: #666;
    line-height: 1.3;
    max-height: 2.6em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 备注编辑样式 */
.notes-textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
}

.notes-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 地图区域 */
.map-area {
    flex: 1;
    position: relative;
}

/* Company Details Panel */
.company-panel,
.cluster-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

.panel-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background-color: #e9ecef;
    color: #333;
}

.panel-content {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

.company-detail {
    margin-bottom: 1.5rem;
}

.company-detail:last-child {
    margin-bottom: 0;
}

.company-detail h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.company-detail-jp {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.detail-label {
    font-weight: 500;
    color: #555;
    min-width: 80px;
    margin-right: 1rem;
}

.detail-value {
    color: #333;
    flex: 1;
}

.company-industry-tag {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
}

/* Cluster Panel Specific */
.cluster-summary {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.cluster-count {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.cluster-area {
    color: #666;
    font-size: 1rem;
}

.cluster-companies {
    max-height: 300px;
    overflow-y: auto;
}

.cluster-company-item {
    padding: 0.75rem;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cluster-company-item:hover {
    background-color: #f8f9fa;
    border-color: #3498db;
}

.cluster-company-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.cluster-company-address {
    font-size: 0.8rem;
    color: #666;
}

/* Map Overlay */
.map-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
    display: none;
}

.map-overlay.show {
    display: block;
}

.company-panel.show,
.cluster-panel.show {
    display: block;
}

/* 自定义地图控件 */
.amap-logo,
.amap-copyright {
    display: none !important;
}

/* 信息窗口样式 */
.amap-info-content {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
}

.amap-info-content h4 {
    font-weight: 600 !important;
    line-height: 1.4 !important;
}

.amap-info-content p {
    line-height: 1.3 !important;
}

/* 地图标记样式 */
.custom-marker {
    background-color: #e74c3c;
    border: 3px solid white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    position: relative;
}

.custom-marker:hover {
    transform: scale(1.2);
    background-color: #c0392b;
}

.custom-marker.active {
    background-color: #f39c12;
    border-color: #fff;
    transform: scale(1.3);
}

/* 拜访状态样式 */
.custom-marker.visited {
    background-color: #27ae60; /* 绿色表示已拜访 */
}

.custom-marker.unvisited {
    background-color: #e74c3c; /* 红色表示未拜访 */
}

.custom-marker.visited.active {
    background-color: #2ecc71; /* 选中的已拜访公司 */
}

.custom-marker.unvisited.active {
    background-color: #f39c12; /* 选中的未拜访公司 */
}

/* 标记点上方的信息标签 */
.marker-label {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    width: 200px;
    text-align: left;
    position: relative;
    margin-bottom: 5px;
}

.marker-label::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: white;
}

.marker-label-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 13px;
    line-height: 1.2;
    white-space: wrap;
}

.marker-label-industry {
    display: inline-block;
    background-color: #3498db;
    color: white;
    padding: 1px 6px;
    border-radius: 8px;
    font-size: 10px;
    margin-bottom: 5px;
}

.marker-label-address {
    color: #666;
    font-size: 11px;
    line-height: 1.2;
    white-space: wrap;
    margin-bottom: 5px;
}

.cluster-marker {
    background-color: #3498db;
    border: 3px solid white;
    border-radius: 50%;
    color: white;
    font-weight: 600;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    padding: 5px;
}

.cluster-marker:hover {
    transform: scale(1.1);
    background-color: #2980b9;
}

.cluster-marker.small {
    width: 30px;
    height: 30px;
    font-size: 10px;
}

.cluster-marker.medium {
    width: 40px;
    height: 40px;
    font-size: 12px;
}

.cluster-marker.large {
    width: 50px;
    height: 50px;
    font-size: 14px;
}

