<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Setup - Company Search System</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        h2 {
            color: #34495e;
            margin-top: 2rem;
        }
        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        ol {
            padding-left: 1.5rem;
        }
        li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Company Search System Demo Setup</h1>
        
        <div class="alert alert-warning">
            <strong>⚠️ Important:</strong> This demo requires an Amap (高德地图) API key to display the interactive map. The search functionality works without an API key.
        </div>

        <h2>🚀 Quick Start</h2>
        <p>You can start exploring the application immediately:</p>
        
        <a href="index.html" class="btn">🔍 Try Search Page</a>
        <a href="map.html" class="btn btn-secondary">🗺️ Try Map Page (requires API key)</a>

        <h2>📋 Setup Instructions</h2>
        
        <h3>1. Get Amap API Key (for map functionality)</h3>
        <ol>
            <li>Visit <a href="https://console.amap.com/" target="_blank">Amap Developer Console</a></li>
            <li>Register for a developer account</li>
            <li>Create a new application</li>
            <li>Copy your JavaScript API key</li>
        </ol>

        <h3>2. Configure API Key</h3>
        <p>Edit <code>map.html</code> and replace <code>YOUR_AMAP_API_KEY</code> with your actual API key:</p>
        
        <div class="code">
&lt;script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=<strong>YOUR_ACTUAL_API_KEY</strong>"&gt;&lt;/script&gt;
        </div>

        <h3>3. Run Local Server (Recommended)</h3>
        <p>For best results, run a local web server:</p>
        
        <div class="code">
# Using Python 3
python -m http.server 8000

# Using Node.js
npx http-server

# Using PHP
php -S localhost:8000
        </div>

        <div class="alert alert-info">
            <strong>💡 Tip:</strong> The search page works without an API key and demonstrates the filtering, pagination, and responsive design features.
        </div>

        <h2>✨ Features to Try</h2>
        
        <h3>Search Page Features:</h3>
        <ul>
            <li>Search by company name (try "東京" or "杭州")</li>
            <li>Filter by industry (製造業, 金融業, IT・技術, etc.)</li>
            <li>Filter by prefecture (浙江省)</li>
            <li>Sort results by name, industry, or location</li>
            <li>Navigate through paginated results</li>
            <li>View company details in modal</li>
            <li>Responsive design - try resizing your browser</li>
        </ul>

        <h3>Map Page Features (with API key):</h3>
        <ul>
            <li>Interactive map centered on Hangzhou</li>
            <li>Company clustering by district when zoomed out</li>
            <li>Individual markers when zoomed in</li>
            <li>Click clusters to see company lists</li>
            <li>Click markers for company details</li>
            <li>Search and filter companies on map</li>
            <li>Navigation between search and map views</li>
        </ul>

        <h2>📊 Sample Data</h2>
        <p>The demo includes 25 sample Japanese companies with offices in Hangzhou, China, including:</p>
        <ul>
            <li>Manufacturing companies (Toyota, Daikin, Komatsu)</li>
            <li>Technology companies (Sony, Panasonic, Hitachi)</li>
            <li>Financial institutions (Tokyo Marine, Sumitomo Mitsui Banking)</li>
            <li>Trading companies (Itochu, Sumitomo Corporation)</li>
            <li>Consumer goods companies (Shiseido, Kao)</li>
        </ul>

        <h2>🔧 Troubleshooting</h2>
        
        <h3>Map not loading?</h3>
        <ul>
            <li>Check that you've added a valid Amap API key</li>
            <li>Ensure you're running from a web server (not file://)</li>
            <li>Check browser console for error messages</li>
        </ul>

        <h3>Search not working?</h3>
        <ul>
            <li>Try different search terms (company names are in Chinese/Japanese)</li>
            <li>Use the dropdown filters instead of text input</li>
            <li>Clear all filters and try again</li>
        </ul>

        <div class="alert alert-info">
            <strong>📝 Note:</strong> This is a demonstration project. In a production environment, you would connect to a real database and implement proper authentication and security measures.
        </div>

        <p style="text-align: center; margin-top: 2rem;">
            <a href="index.html" class="btn">🔍 Start with Search Page</a>
        </p>
    </div>
</body>
</html>
