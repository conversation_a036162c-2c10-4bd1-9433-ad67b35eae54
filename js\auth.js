// 认证管理工具类
class AuthManager {
    constructor() {
        this.initializeAuth();
    }
    
    /**
     * 初始化认证状态
     */
    initializeAuth() {
        // 检查登录状态
        this.checkAuthStatus();
        
        // 绑定登出事件
        this.bindLogoutEvent();
        
        // 定期检查token有效性
        this.startTokenValidation();
    }
    
    /**
     * 检查认证状态
     */
    checkAuthStatus() {
        const token = localStorage.getItem('authToken');
        const userInfo = localStorage.getItem('userInfo');
        const loginTime = localStorage.getItem('loginTime');
        
        if (token && userInfo && loginTime) {
            const elapsed = Date.now() - parseInt(loginTime);
            const maxAge = 24 * 60 * 60 * 1000; // 24小时
            
            if (elapsed < maxAge) {
                // 登录状态有效
                this.showUserInfo(userInfo);
                return true;
            } else {
                // 登录状态过期
                this.clearAuthData();
                return false;
            }
        }
        
        // 未登录状态
        this.showLoginLink();
        return false;
    }
    
    /**
     * 显示用户信息
     */
    showUserInfo(userInfo) {
        const navUser = document.getElementById('navUser');
        const loginLink = document.getElementById('loginLink');
        const userName = document.getElementById('userName');
        
        if (navUser && loginLink && userName) {
            userName.textContent = userInfo || 'ユーザー';
            navUser.style.display = 'flex';
            loginLink.style.display = 'none';
        }
    }
    
    /**
     * 显示登录链接
     */
    showLoginLink() {
        const navUser = document.getElementById('navUser');
        const loginLink = document.getElementById('loginLink');
        
        if (navUser && loginLink) {
            navUser.style.display = 'none';
            loginLink.style.display = 'block';
        }
    }
    
    /**
     * 绑定登出事件
     */
    bindLogoutEvent() {
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }
    }
    
    /**
     * 登出功能
     */
    logout() {
        // 确认登出
        if (confirm('ログアウトしますか？')) {
            this.clearAuthData();
            this.showLoginLink();
            
            // 跳转到登录页面
            window.location.href = 'login.html';
        }
    }
    
    /**
     * 清除认证数据
     */
    clearAuthData() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('loginTime');
        sessionStorage.removeItem('isLoggedIn');
        sessionStorage.removeItem('currentUser');
    }
    
    /**
     * 开始token验证
     */
    startTokenValidation() {
        // 每5分钟检查一次token有效性
        setInterval(() => {
            this.checkAuthStatus();
        }, 5 * 60 * 1000);
    }
    
    /**
     * 获取认证token
     */
    getAuthToken() {
        return localStorage.getItem('authToken');
    }
    
    /**
     * 获取用户信息
     */
    getUserInfo() {
        const userInfo = localStorage.getItem('userInfo');
        return userInfo ? JSON.parse(userInfo) : null;
    }
    
    /**
     * 检查是否已登录
     */
    isAuthenticated() {
        return this.checkAuthStatus();
    }
    
    /**
     * 要求登录（用于受保护的页面）
     */
    requireAuth() {
        if (!this.isAuthenticated()) {
            alert('この機能を使用するにはログインが必要です。');
            window.location.href = 'login.html';
            return false;
        }
        return true;
    }
    
    /**
     * 添加认证头到请求
     */
    addAuthHeaders(headers = {}) {
        const token = this.getAuthToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        return headers;
    }
    
    /**
     * 发送认证请求
     */
    async authenticatedFetch(url, options = {}) {
        // 检查认证状态
        if (!this.isAuthenticated()) {
            throw new Error('認証が必要です');
        }
        
        // 添加认证头
        options.headers = this.addAuthHeaders(options.headers || {});
        
        try {
            const response = await fetch(url, options);
            
            // 检查认证错误
            if (response.status === 401) {
                this.clearAuthData();
                this.showLoginLink();
                throw new Error('認証の有効期限が切れました');
            }
            
            return response;
        } catch (error) {
            console.error('认证请求失败:', error);
            throw error;
        }
    }
}

// 创建全局认证管理器实例
window.AuthManager = new AuthManager();

// 页面加载完成后初始化认证状态
document.addEventListener('DOMContentLoaded', () => {
    if (window.AuthManager) {
        window.AuthManager.checkAuthStatus();
    }
});
